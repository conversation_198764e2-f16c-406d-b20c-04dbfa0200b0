2025-05-26 16:23:14,535 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-26 16:23:14,536 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-26 16:23:14,553 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-26 16:23:14,559 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-26 16:23:49,604 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-26 16:23:49,605 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-26 16:23:49,625 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-26 16:23:49,633 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 00:40:42,328 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 00:40:42,329 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 00:40:42,511 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 00:43:42,496 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 00:43:42,496 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 00:43:42,515 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:10:59,361 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 01:10:59,361 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 01:10:59,383 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:20:24,405 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 01:20:24,406 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 01:20:24,433 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:56:14,165 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 01:56:14,166 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 01:56:14,189 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 17:07:46,794 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 17:07:46,794 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 17:07:46,961 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 09:22:43,544 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 09:22:43,545 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 09:22:43,739 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:08:05,645 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:08:05,645 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:08:05,768 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:13:46,298 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:13:46,299 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:13:46,322 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:18:40,902 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:18:40,903 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:18:40,927 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:20:46,023 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:20:46,024 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:20:46,044 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:25:48,706 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:25:48,706 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:25:48,726 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:35:04,867 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:35:04,868 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:35:04,885 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:38:19,904 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:38:19,905 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:38:19,924 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:45:46,950 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:45:46,950 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:45:46,971 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:54:20,707 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:54:20,708 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:54:20,872 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:58:47,922 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:58:47,922 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:58:48,053 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 11:00:31,179 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 11:00:31,180 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 11:00:31,204 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 14:37:18,692 - service_runner - INFO - Service environment set up. Working directory: C:\Users\<USER>\OneDrive\Documents\SENECA\SPR888\REPOSITORIES\backend
2025-06-10 14:37:18,694 - service_runner - INFO - Python path: ['C:\\Users\\<USER>\\OneDrive\\Documents\\SENECA\\SPR888\\REPOSITORIES\\backend', 'C:\\Users\\<USER>\\OneDrive\\Documents\\SENECA\\SPR888\\REPOSITORIES\\backend', 'C:\\Python312\\python312.zip']...
2025-06-10 14:37:18,915 - config.config_manager - INFO - Configuration loaded from C:\Users\<USER>\OneDrive\Documents\SENECA\SPR888\REPOSITORIES\backend\config\default_config.yaml
