const mongoose = require('mongoose');
const Log = require('./backend/src/models/Log');

async function testLogSave() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/exlog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');

    // Create a test log
    const testLog = new Log({
      logId: 'test_debug_' + Date.now(),
      timestamp: new Date(),
      source: 'System',
      sourceType: 'event',
      host: 'test-host',
      logLevel: 'info',
      message: 'Test log message for debugging',
      metadata: {
        agentId: '507f1f77bcf86cd799439011',
        collectionTime: new Date(),
        processingTime: Date.now(),
      }
    });

    console.log('📝 Created test log object');
    console.log('Log data:', JSON.stringify(testLog.toObject(), null, 2));

    // Try to save
    const savedLog = await testLog.save();
    console.log('✅ Log saved successfully!');
    console.log('Saved log ID:', savedLog._id);

    // Verify it's in the database
    const count = await Log.countDocuments();
    console.log('📊 Total logs in database:', count);

    // Find the saved log
    const foundLog = await Log.findById(savedLog._id);
    console.log('🔍 Found saved log:', foundLog ? 'Yes' : 'No');

  } catch (error) {
    console.error('❌ Error:', error);
    console.error('Error details:', error.message);
    if (error.errors) {
      console.error('Validation errors:', error.errors);
    }
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

testLogSave();
