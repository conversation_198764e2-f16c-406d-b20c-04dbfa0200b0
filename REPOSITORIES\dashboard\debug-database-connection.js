const mongoose = require('mongoose');
const Log = require('./backend/src/models/Log');

async function debugDatabaseConnection() {
  try {
    console.log('🔍 Testing database connection and log retrieval...');

    // Connect to MongoDB using localhost (since we're outside Docker)
    await mongoose.connect('mongodb://localhost:27017/exlog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB (mongodb:27017/exlog)');

    // Test 1: Count all logs
    const totalLogs = await Log.countDocuments();
    console.log('📊 Total logs in database:', totalLogs);

    // Test 2: Get recent logs
    const recentLogs = await Log.find()
      .sort({ timestamp: -1 })
      .limit(5)
      .lean();
    
    console.log('📋 Recent logs found:', recentLogs.length);
    
    if (recentLogs.length > 0) {
      console.log('\n🔍 Sample logs:');
      recentLogs.forEach((log, index) => {
        console.log(`${index + 1}. ${log.logId} - ${log.message} (${log.timestamp})`);
      });
    }

    // Test 3: Try the same query the API uses
    const apiQuery = {};
    const apiLogs = await Log.find(apiQuery)
      .sort({ timestamp: -1 })
      .skip(0)
      .limit(100)
      .lean();
    
    console.log('🔍 API-style query results:', apiLogs.length);

    // Test 4: Check database name
    console.log('🗄️ Current database:', mongoose.connection.db.databaseName);
    
    // Test 5: List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('📁 Collections in database:', collections.map(c => c.name));

  } catch (error) {
    console.error('❌ Error:', error);
    console.error('Error details:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

debugDatabaseConnection();
