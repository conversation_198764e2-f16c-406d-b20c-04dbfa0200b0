const axios = require('axios');

async function testAgentLogFormat() {
  try {
    // Simulate the exact log format that the Windows agent sends
    const agentLogData = {
      logs: [
        {
          logId: 'test_agent_' + Date.now(),
          timestamp: new Date().toISOString(),
          source: 'System',
          sourceType: 'event',
          host: 'DESKTOP-TEST',
          logLevel: 'info',
          message: 'Test log from simulated Windows agent',
          rawData: 'Raw event log data here',
          additionalFields: {
            eventId: '1001',
            processId: '1234',
            threadId: '5678'
          }
        }
      ]
    };

    console.log('📤 Sending log data to dashboard API...');
    console.log('Log data:', JSON.stringify(agentLogData, null, 2));

    // Send to the dashboard API
    const response = await axios.post('http://localhost:5000/api/v1/logs', agentLogData, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'bb93493a1a62ace8d41d25cf233f67b5cb8dbdd709d5617c914c299bc5e4e9a0'
      },
      timeout: 10000
    });

    console.log('✅ Response received!');
    console.log('Status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Error sending log:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testAgentLogFormat();
