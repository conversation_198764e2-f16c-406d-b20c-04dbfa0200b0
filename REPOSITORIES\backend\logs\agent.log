2025-05-26 16:23:14 - root - INFO - Event Log Collector initialized
2025-05-26 16:23:14 - root - INFO - Application Log Collector initialized
2025-05-26 16:23:14 - root - INFO - System Log Collector initialized
2025-05-26 16:23:14 - root - INFO - Network Log Collector initialized
2025-05-26 16:23:14 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-26 16:23:14 - root - INFO - Packet Collector initialized
2025-05-26 16:23:14 - root - INFO - Logging Agent initialized successfully
2025-05-26 16:23:14 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-26 16:23:14 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-26 16:23:14 - root - INFO - Logging Agent started successfully
2025-05-26 16:23:14 - audit_logger - INFO - Python Logging Agent service started
2025-05-26 16:23:19 - root - INFO - Stopping Logging Agent...
2025-05-26 16:23:24 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-26 16:23:25 - root - INFO - Logging Agent stopped successfully
2025-05-26 16:23:25 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-26 16:23:49 - root - INFO - Event Log Collector initialized
2025-05-26 16:23:49 - root - INFO - Application Log Collector initialized
2025-05-26 16:23:49 - root - INFO - System Log Collector initialized
2025-05-26 16:23:49 - root - INFO - Network Log Collector initialized
2025-05-26 16:23:49 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-26 16:23:49 - root - INFO - Packet Collector initialized
2025-05-26 16:23:49 - root - INFO - Logging Agent initialized successfully
2025-05-26 16:23:49 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-26 16:23:49 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-26 16:23:49 - root - INFO - Logging Agent started successfully
2025-05-26 16:23:49 - audit_logger - INFO - Python Logging Agent service started
2025-05-26 16:23:54 - root - INFO - Stopping Logging Agent...
2025-05-26 16:23:59 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-26 16:24:00 - root - INFO - Logging Agent stopped successfully
2025-05-26 16:24:00 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 00:36:59 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 00:36:59 - utils.api_client - INFO - Async API client started
2025-05-30 00:36:59 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 00:36:59 - utils.api_client - WARNING - API connection test failed: HTTP 401: {"error":"Unauthorized","message":"Invalid API key"}
2025-05-30 00:36:59 - root - WARNING - API connection test failed: HTTP 401: {"error":"Unauthorized","message":"Invalid API key"}
2025-05-30 00:36:59 - root - INFO - Event Log Collector initialized
2025-05-30 00:36:59 - root - INFO - Logging Agent initialized successfully
2025-05-30 00:36:59 - root - INFO - Logging Agent started successfully
2025-05-30 00:36:59 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 00:37:04 - utils.api_client - ERROR - API authentication failed - check API key
2025-05-30 00:37:04 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 00:37:04 - utils.api_client - WARNING - Sent 0/10 logs to API
2025-05-30 00:37:09 - root - INFO - Stopping Logging Agent...
2025-05-30 00:37:09 - utils.api_client - ERROR - API authentication failed - check API key
2025-05-30 00:37:09 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 00:37:09 - utils.api_client - WARNING - Sent 0/10 logs to API
2025-05-30 00:37:10 - utils.api_client - INFO - Async API client stopped
2025-05-30 00:37:10 - root - INFO - Logging Agent stopped successfully
2025-05-30 00:37:10 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 00:40:42 - root - INFO - Event Log Collector initialized
2025-05-30 00:40:42 - root - INFO - Application Log Collector initialized
2025-05-30 00:40:42 - root - INFO - System Log Collector initialized
2025-05-30 00:40:42 - root - INFO - Network Log Collector initialized
2025-05-30 00:40:42 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 00:40:42 - root - INFO - Packet Collector initialized
2025-05-30 00:40:42 - root - INFO - Logging Agent initialized successfully
2025-05-30 00:40:42 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 00:40:42 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 00:40:42 - root - INFO - Logging Agent started successfully
2025-05-30 00:40:42 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 00:41:23 - root - INFO - Stopping Logging Agent...
2025-05-30 00:41:28 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 00:41:29 - root - INFO - Logging Agent stopped successfully
2025-05-30 00:41:29 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 00:43:42 - root - INFO - Event Log Collector initialized
2025-05-30 00:43:42 - root - INFO - Application Log Collector initialized
2025-05-30 00:43:42 - root - INFO - System Log Collector initialized
2025-05-30 00:43:42 - root - INFO - Network Log Collector initialized
2025-05-30 00:43:42 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 00:43:42 - root - INFO - Packet Collector initialized
2025-05-30 00:43:42 - root - INFO - Logging Agent initialized successfully
2025-05-30 00:43:42 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 00:43:42 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 00:43:42 - root - INFO - Logging Agent started successfully
2025-05-30 00:43:42 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 00:44:00 - logging_agent.collectors.event_log_collector - WARNING - Access test failed for Application: (6, 'CloseEventLog', 'The handle is invalid.')
2025-05-30 00:44:34 - root - INFO - Stopping Logging Agent...
2025-05-30 00:44:39 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 00:44:40 - root - INFO - Logging Agent stopped successfully
2025-05-30 00:44:40 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 00:48:51 - root - DEBUG - API output disabled in configuration
2025-05-30 00:48:51 - root - INFO - Event Log Collector initialized
2025-05-30 00:48:51 - root - INFO - Application Log Collector initialized
2025-05-30 00:48:51 - root - INFO - System Log Collector initialized
2025-05-30 00:48:52 - root - INFO - Network Log Collector initialized
2025-05-30 00:48:52 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 00:48:52 - root - INFO - Packet Collector initialized
2025-05-30 00:48:52 - root - DEBUG - Signal handlers set up successfully
2025-05-30 00:48:52 - root - INFO - Logging Agent initialized successfully
2025-05-30 00:49:05 - root - DEBUG - API output disabled in configuration
2025-05-30 00:49:05 - root - INFO - Event Log Collector initialized
2025-05-30 00:49:05 - root - INFO - Application Log Collector initialized
2025-05-30 00:49:05 - root - INFO - System Log Collector initialized
2025-05-30 00:49:05 - root - INFO - Network Log Collector initialized
2025-05-30 00:49:05 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 00:49:05 - root - INFO - Packet Collector initialized
2025-05-30 00:49:05 - root - DEBUG - Signal handlers set up successfully
2025-05-30 00:49:05 - root - INFO - Logging Agent initialized successfully
2025-05-30 00:49:39 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 00:49:39 - utils.api_client - INFO - Async API client started
2025-05-30 00:49:39 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 00:49:39 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 00:49:39 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 401 52
2025-05-30 00:49:39 - utils.api_client - WARNING - API connection test failed: HTTP 401: {"error":"Unauthorized","message":"Invalid API key"}
2025-05-30 00:49:39 - root - WARNING - API connection test failed: HTTP 401: {"error":"Unauthorized","message":"Invalid API key"}
2025-05-30 00:49:39 - root - INFO - Event Log Collector initialized
2025-05-30 00:49:39 - root - INFO - Application Log Collector initialized
2025-05-30 00:49:39 - root - INFO - System Log Collector initialized
2025-05-30 00:49:39 - root - INFO - Network Log Collector initialized
2025-05-30 00:49:39 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 00:49:39 - root - INFO - Packet Collector initialized
2025-05-30 00:49:39 - root - DEBUG - Signal handlers set up successfully
2025-05-30 00:49:39 - root - INFO - Logging Agent initialized successfully
2025-05-30 00:49:39 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 00:49:39 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 00:49:39 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 401 52
2025-05-30 00:49:39 - utils.api_client - WARNING - API connection test failed: HTTP 401: {"error":"Unauthorized","message":"Invalid API key"}
2025-05-30 01:09:40 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:09:40 - utils.api_client - INFO - Async API client started
2025-05-30 01:09:40 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:09:40 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:09:40 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:09:40 - utils.api_client - INFO - API connection test successful
2025-05-30 01:09:40 - root - INFO - API connection test successful (response time: 0.05s)
2025-05-30 01:09:40 - root - INFO - Event Log Collector initialized
2025-05-30 01:09:40 - root - INFO - Application Log Collector initialized
2025-05-30 01:09:40 - root - INFO - System Log Collector initialized
2025-05-30 01:09:41 - root - INFO - Network Log Collector initialized
2025-05-30 01:09:41 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:09:41 - root - INFO - Packet Collector initialized
2025-05-30 01:09:41 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:09:41 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:10:59 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:10:59 - utils.api_client - INFO - Async API client started
2025-05-30 01:10:59 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:10:59 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:10:59 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:10:59 - utils.api_client - INFO - API connection test successful
2025-05-30 01:10:59 - root - INFO - API connection test successful (response time: 0.07s)
2025-05-30 01:10:59 - root - INFO - Event Log Collector initialized
2025-05-30 01:10:59 - root - INFO - Application Log Collector initialized
2025-05-30 01:10:59 - root - INFO - System Log Collector initialized
2025-05-30 01:10:59 - root - INFO - Network Log Collector initialized
2025-05-30 01:10:59 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:10:59 - root - INFO - Packet Collector initialized
2025-05-30 01:10:59 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:10:59 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:10:59 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:10:59 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:10:59 - root - INFO - Logging Agent started successfully
2025-05-30 01:10:59 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:10:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:10:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:10:59 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:10:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:10:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:10:59 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:10:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:10:59 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:10:59 - root - DEBUG - Memory usage for LoggingAgent: 84.73 MB
2025-05-30 01:11:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for System
2025-05-30 01:11:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for Application
2025-05-30 01:11:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for Application
2025-05-30 01:11:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:11:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for System
2025-05-30 01:11:04 - root - DEBUG - Queued 54 logs for API transmission
2025-05-30 01:11:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:11:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:04 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:11:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:11:04 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:11:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:11:04 - root - DEBUG - Collected 6 logs from network_logs
2025-05-30 01:11:04 - root - DEBUG - Total logs collected this cycle: 60
2025-05-30 01:11:04 - root - DEBUG - Memory usage for LoggingAgent: 85.09 MB
2025-05-30 01:11:09 - root - DEBUG - Queued 60 logs for API transmission
2025-05-30 01:11:09 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:11:09 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:11:09 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:11:09 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:11:09 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:11:09 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:11:09 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:11:09 - utils.api_client - WARNING - Sent 0/114 logs to API
2025-05-30 01:11:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:11:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:09 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:11:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:11:09 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:11:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:11:09 - root - DEBUG - Collected 5 logs from network_logs
2025-05-30 01:11:09 - root - DEBUG - Total logs collected this cycle: 59
2025-05-30 01:11:09 - root - DEBUG - Memory usage for LoggingAgent: 85.54 MB
2025-05-30 01:11:14 - root - DEBUG - Queued 59 logs for API transmission
2025-05-30 01:11:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:11:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:14 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:11:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:11:14 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:11:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:11:14 - root - DEBUG - Collected 5 logs from network_logs
2025-05-30 01:11:14 - root - DEBUG - Total logs collected this cycle: 59
2025-05-30 01:11:14 - root - DEBUG - Memory usage for LoggingAgent: 85.56 MB
2025-05-30 01:11:19 - root - DEBUG - Queued 59 logs for API transmission
2025-05-30 01:11:19 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:11:19 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:11:19 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:11:19 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:11:19 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:11:19 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:11:19 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:11:19 - utils.api_client - WARNING - Sent 0/118 logs to API
2025-05-30 01:11:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:11:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:19 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:11:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:11:19 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:11:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:11:19 - root - DEBUG - Collected 8 logs from network_logs
2025-05-30 01:11:19 - root - DEBUG - Total logs collected this cycle: 62
2025-05-30 01:11:19 - root - DEBUG - Memory usage for LoggingAgent: 86.19 MB
2025-05-30 01:11:24 - root - DEBUG - Queued 62 logs for API transmission
2025-05-30 01:11:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:11:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:24 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:11:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:11:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:11:24 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:11:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:11:24 - root - DEBUG - Collected 3 logs from network_logs
2025-05-30 01:11:24 - root - DEBUG - Total logs collected this cycle: 57
2025-05-30 01:11:24 - root - DEBUG - Memory usage for LoggingAgent: 86.26 MB
2025-05-30 01:11:27 - root - INFO - Stopping Logging Agent...
2025-05-30 01:11:29 - root - DEBUG - Queued 57 logs for API transmission
2025-05-30 01:11:29 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:11:29 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:11:29 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:11:29 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:11:29 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:11:29 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:11:29 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:11:29 - utils.api_client - WARNING - Sent 0/119 logs to API
2025-05-30 01:11:32 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:11:33 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:11:33 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:11:33 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:15:06 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:15:06 - utils.api_client - INFO - Async API client started
2025-05-30 01:15:06 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:15:06 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:15:06 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:15:06 - utils.api_client - INFO - API connection test successful
2025-05-30 01:15:06 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:15:06 - root - INFO - Event Log Collector initialized
2025-05-30 01:15:06 - root - INFO - Application Log Collector initialized
2025-05-30 01:15:06 - root - INFO - System Log Collector initialized
2025-05-30 01:15:06 - root - INFO - Network Log Collector initialized
2025-05-30 01:15:06 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:15:06 - root - INFO - Packet Collector initialized
2025-05-30 01:15:06 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:15:06 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:15:06 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:15:06 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:15:06 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 182
2025-05-30 01:15:06 - utils.api_client - ERROR - API request failed with status 201: {"status":"success","message":"Processed 1 logs successfully","data":{"processed":1,"failed":0,"results":{"successful":[{"logId":"test-direct-001","status":"success"}],"failed":[]}}}
2025-05-30 01:15:06 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:15:06 - utils.api_client - WARNING - Sent 0/1 logs to API
2025-05-30 01:15:06 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:15:06 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:15:06 - utils.api_client - INFO - Async API client started
2025-05-30 01:15:06 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:15:06 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:15:06 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:15:06 - utils.api_client - INFO - API connection test successful
2025-05-30 01:15:06 - root - INFO - API connection test successful (response time: 0.04s)
2025-05-30 01:15:06 - root - INFO - Event Log Collector initialized
2025-05-30 01:15:06 - root - INFO - Application Log Collector initialized
2025-05-30 01:15:06 - root - INFO - System Log Collector initialized
2025-05-30 01:15:06 - root - INFO - Network Log Collector initialized
2025-05-30 01:15:06 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:15:06 - root - INFO - Packet Collector initialized
2025-05-30 01:15:06 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:15:06 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:15:06 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:15:06 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:15:06 - root - INFO - Logging Agent started successfully
2025-05-30 01:15:06 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:15:06 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:15:06 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:15:06 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:15:06 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:15:06 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:15:06 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:15:06 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:15:06 - root - DEBUG - Collected 1 logs from network_logs
2025-05-30 01:15:06 - root - DEBUG - Total logs collected this cycle: 55
2025-05-30 01:15:06 - root - DEBUG - Memory usage for LoggingAgent: 84.75 MB
2025-05-30 01:15:57 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:15:57 - utils.api_client - INFO - Async API client started
2025-05-30 01:15:57 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:15:57 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:15:57 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:15:57 - utils.api_client - INFO - API connection test successful
2025-05-30 01:15:57 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:15:57 - root - INFO - Event Log Collector initialized
2025-05-30 01:15:57 - root - INFO - Application Log Collector initialized
2025-05-30 01:15:57 - root - INFO - System Log Collector initialized
2025-05-30 01:15:57 - root - INFO - Network Log Collector initialized
2025-05-30 01:15:57 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:15:57 - root - INFO - Packet Collector initialized
2025-05-30 01:15:57 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:15:57 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:15:57 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:15:57 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:15:57 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 182
2025-05-30 01:15:57 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:15:57 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:15:57 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:15:57 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:15:57 - utils.api_client - INFO - Async API client started
2025-05-30 01:15:57 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:15:57 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:15:57 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:15:57 - utils.api_client - INFO - API connection test successful
2025-05-30 01:15:57 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:15:57 - root - INFO - Event Log Collector initialized
2025-05-30 01:15:57 - root - INFO - Application Log Collector initialized
2025-05-30 01:15:57 - root - INFO - System Log Collector initialized
2025-05-30 01:15:57 - root - INFO - Network Log Collector initialized
2025-05-30 01:15:57 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:15:57 - root - INFO - Packet Collector initialized
2025-05-30 01:15:57 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:15:57 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:15:57 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:15:57 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:15:57 - root - INFO - Logging Agent started successfully
2025-05-30 01:15:57 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:15:57 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:15:57 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:15:57 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:15:57 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:15:57 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:15:57 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:15:57 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:15:58 - root - DEBUG - Collected 1 logs from network_logs
2025-05-30 01:15:58 - root - DEBUG - Total logs collected this cycle: 55
2025-05-30 01:15:58 - root - DEBUG - Memory usage for LoggingAgent: 85.62 MB
2025-05-30 01:16:11 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:16:11 - utils.api_client - INFO - Async API client started
2025-05-30 01:16:11 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:16:11 - utils.api_client - INFO - API connection test successful
2025-05-30 01:16:11 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:16:11 - root - INFO - Event Log Collector initialized
2025-05-30 01:16:11 - root - INFO - Application Log Collector initialized
2025-05-30 01:16:11 - root - INFO - System Log Collector initialized
2025-05-30 01:16:11 - root - INFO - Network Log Collector initialized
2025-05-30 01:16:11 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:16:11 - root - INFO - Packet Collector initialized
2025-05-30 01:16:11 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:16:11 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:16:11 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:16:11 - utils.api_client - INFO - API connection test successful
2025-05-30 01:16:11 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:16:11 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:16:11 - utils.api_client - INFO - Async API client started
2025-05-30 01:16:11 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:16:11 - utils.api_client - INFO - API connection test successful
2025-05-30 01:16:11 - root - INFO - API connection test successful (response time: 0.05s)
2025-05-30 01:16:11 - root - INFO - Event Log Collector initialized
2025-05-30 01:16:11 - root - INFO - Application Log Collector initialized
2025-05-30 01:16:11 - root - INFO - System Log Collector initialized
2025-05-30 01:16:11 - root - INFO - Network Log Collector initialized
2025-05-30 01:16:11 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:16:11 - root - INFO - Packet Collector initialized
2025-05-30 01:16:11 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:16:11 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:16:11 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:16:11 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:16:11 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:16:11 - utils.api_client - WARNING - Sent 0/2 logs to API
2025-05-30 01:16:11 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:16:11 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:16:11 - utils.api_client - INFO - Async API client started
2025-05-30 01:16:11 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:16:11 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:16:11 - utils.api_client - INFO - API connection test successful
2025-05-30 01:16:11 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:16:11 - root - INFO - Event Log Collector initialized
2025-05-30 01:16:11 - root - INFO - Application Log Collector initialized
2025-05-30 01:16:11 - root - INFO - System Log Collector initialized
2025-05-30 01:16:11 - root - INFO - Network Log Collector initialized
2025-05-30 01:16:11 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:16:11 - root - INFO - Packet Collector initialized
2025-05-30 01:16:11 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:16:11 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:16:11 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:16:11 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:16:11 - root - INFO - Logging Agent started successfully
2025-05-30 01:16:11 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:16:11 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:16:11 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:16:11 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:16:11 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:16:11 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:16:11 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:16:11 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:16:11 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:16:11 - root - DEBUG - Memory usage for LoggingAgent: 85.26 MB
2025-05-30 01:16:16 - root - DEBUG - Queued 54 logs for API transmission
2025-05-30 01:16:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:16:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:16:16 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:16:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:16:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:16:16 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:16:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:16:16 - root - DEBUG - Collected 11 logs from network_logs
2025-05-30 01:16:16 - root - DEBUG - Total logs collected this cycle: 65
2025-05-30 01:16:16 - root - DEBUG - Memory usage for LoggingAgent: 85.46 MB
2025-05-30 01:16:21 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:16:21 - root - DEBUG - Queued 65 logs for API transmission
2025-05-30 01:16:21 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:16:21 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:16:21 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:16:21 - utils.api_client - WARNING - Sent 0/54 logs to API
2025-05-30 01:16:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:16:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:16:21 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:16:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:16:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:16:21 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:16:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:16:21 - root - DEBUG - Collected 6 logs from network_logs
2025-05-30 01:16:21 - root - DEBUG - Total logs collected this cycle: 60
2025-05-30 01:16:21 - root - DEBUG - Memory usage for LoggingAgent: 85.84 MB
2025-05-30 01:16:26 - root - DEBUG - Queued 60 logs for API transmission
2025-05-30 01:16:26 - root - INFO - Stopping Logging Agent...
2025-05-30 01:16:31 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:16:31 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:16:31 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:16:31 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:16:31 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:16:31 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:16:31 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:16:31 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:16:31 - utils.api_client - WARNING - Sent 0/125 logs to API
2025-05-30 01:16:32 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:16:32 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:16:32 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:17:41 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:17:41 - utils.api_client - INFO - Async API client started
2025-05-30 01:17:41 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:17:41 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:17:41 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:17:41 - utils.api_client - INFO - API connection test successful
2025-05-30 01:17:41 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:17:41 - root - INFO - Event Log Collector initialized
2025-05-30 01:17:41 - root - INFO - Application Log Collector initialized
2025-05-30 01:17:41 - root - INFO - System Log Collector initialized
2025-05-30 01:17:42 - root - INFO - Network Log Collector initialized
2025-05-30 01:17:42 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:17:42 - root - INFO - Packet Collector initialized
2025-05-30 01:17:42 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:17:42 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:17:42 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:17:42 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:17:42 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 182
2025-05-30 01:17:42 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:17:42 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:17:42 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:17:42 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:17:42 - utils.api_client - INFO - Async API client started
2025-05-30 01:17:42 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:17:42 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:17:42 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:17:42 - utils.api_client - INFO - API connection test successful
2025-05-30 01:17:42 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:17:42 - root - INFO - Event Log Collector initialized
2025-05-30 01:17:42 - root - INFO - Application Log Collector initialized
2025-05-30 01:17:42 - root - INFO - System Log Collector initialized
2025-05-30 01:17:42 - root - INFO - Network Log Collector initialized
2025-05-30 01:17:42 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:17:42 - root - INFO - Packet Collector initialized
2025-05-30 01:17:42 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:17:42 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:17:42 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:17:42 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:17:42 - root - INFO - Logging Agent started successfully
2025-05-30 01:17:42 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:17:42 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:17:42 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:17:42 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:17:42 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:17:42 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:17:42 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:17:42 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:17:42 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:17:42 - root - DEBUG - Memory usage for LoggingAgent: 84.67 MB
2025-05-30 01:17:55 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:17:55 - utils.api_client - INFO - Async API client started
2025-05-30 01:17:55 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:17:55 - utils.api_client - INFO - API connection test successful
2025-05-30 01:17:55 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:17:55 - root - INFO - Event Log Collector initialized
2025-05-30 01:17:55 - root - INFO - Application Log Collector initialized
2025-05-30 01:17:55 - root - INFO - System Log Collector initialized
2025-05-30 01:17:55 - root - INFO - Network Log Collector initialized
2025-05-30 01:17:55 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:17:55 - root - INFO - Packet Collector initialized
2025-05-30 01:17:55 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:17:55 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:17:55 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:17:55 - utils.api_client - INFO - API connection test successful
2025-05-30 01:17:55 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:17:55 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:17:55 - utils.api_client - INFO - Async API client started
2025-05-30 01:17:55 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:17:55 - utils.api_client - INFO - API connection test successful
2025-05-30 01:17:55 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:17:55 - root - INFO - Event Log Collector initialized
2025-05-30 01:17:55 - root - INFO - Application Log Collector initialized
2025-05-30 01:17:55 - root - INFO - System Log Collector initialized
2025-05-30 01:17:55 - root - INFO - Network Log Collector initialized
2025-05-30 01:17:55 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:17:55 - root - INFO - Packet Collector initialized
2025-05-30 01:17:55 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:17:55 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:17:55 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:17:55 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:17:55 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:17:55 - utils.api_client - WARNING - Sent 0/2 logs to API
2025-05-30 01:17:55 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:17:55 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:17:55 - utils.api_client - INFO - Async API client started
2025-05-30 01:17:55 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:17:55 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:17:55 - utils.api_client - INFO - API connection test successful
2025-05-30 01:17:55 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:17:55 - root - INFO - Event Log Collector initialized
2025-05-30 01:17:55 - root - INFO - Application Log Collector initialized
2025-05-30 01:17:55 - root - INFO - System Log Collector initialized
2025-05-30 01:17:55 - root - INFO - Network Log Collector initialized
2025-05-30 01:17:55 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:17:55 - root - INFO - Packet Collector initialized
2025-05-30 01:17:55 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:17:55 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:17:55 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:17:55 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:17:55 - root - INFO - Logging Agent started successfully
2025-05-30 01:17:55 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:17:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:17:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:17:55 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:17:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:17:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:17:55 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:17:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:17:56 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:17:56 - root - DEBUG - Memory usage for LoggingAgent: 85.51 MB
2025-05-30 01:18:00 - root - DEBUG - Queued 54 logs for API transmission
2025-05-30 01:18:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:18:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:18:00 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:18:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:18:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:18:00 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:18:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:18:01 - root - DEBUG - Collected 22 logs from network_logs
2025-05-30 01:18:01 - root - DEBUG - Total logs collected this cycle: 76
2025-05-30 01:18:01 - root - DEBUG - Memory usage for LoggingAgent: 86.56 MB
2025-05-30 01:18:05 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:18:05 - root - DEBUG - Queued 76 logs for API transmission
2025-05-30 01:18:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:18:05 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:18:05 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:18:05 - utils.api_client - WARNING - Sent 0/54 logs to API
2025-05-30 01:18:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:18:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:18:05 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:18:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:18:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:18:05 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:18:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:18:06 - root - DEBUG - Collected 19 logs from network_logs
2025-05-30 01:18:06 - root - DEBUG - Total logs collected this cycle: 73
2025-05-30 01:18:06 - root - DEBUG - Memory usage for LoggingAgent: 86.72 MB
2025-05-30 01:18:10 - root - DEBUG - Queued 73 logs for API transmission
2025-05-30 01:18:10 - root - INFO - Stopping Logging Agent...
2025-05-30 01:18:15 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:18:15 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:18:15 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:18:15 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:18:15 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:18:15 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:18:15 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:18:15 - utils.api_client - WARNING - Sent 0/149 logs to API
2025-05-30 01:18:15 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:18:16 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:18:16 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:18:16 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:18:49 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:18:49 - utils.api_client - INFO - Async API client started
2025-05-30 01:18:49 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:18:49 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:18:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:18:49 - utils.api_client - INFO - API connection test successful
2025-05-30 01:18:49 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:18:49 - root - INFO - Event Log Collector initialized
2025-05-30 01:18:49 - root - INFO - Application Log Collector initialized
2025-05-30 01:18:49 - root - INFO - System Log Collector initialized
2025-05-30 01:18:49 - root - INFO - Network Log Collector initialized
2025-05-30 01:18:49 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:18:49 - root - INFO - Packet Collector initialized
2025-05-30 01:18:49 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:18:49 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:18:49 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:18:49 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:18:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:18:49 - utils.api_client - INFO - API connection test successful
2025-05-30 01:18:49 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:18:49 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:18:49 - utils.api_client - INFO - Async API client started
2025-05-30 01:18:49 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:18:49 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:18:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:18:49 - utils.api_client - INFO - API connection test successful
2025-05-30 01:18:49 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:18:49 - root - INFO - Event Log Collector initialized
2025-05-30 01:18:49 - root - INFO - Application Log Collector initialized
2025-05-30 01:18:49 - root - INFO - System Log Collector initialized
2025-05-30 01:18:50 - root - INFO - Network Log Collector initialized
2025-05-30 01:18:50 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:18:50 - root - INFO - Packet Collector initialized
2025-05-30 01:18:50 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:18:50 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:18:50 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:18:50 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:18:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:18:50 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:18:50 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:18:50 - utils.api_client - WARNING - Sent 0/2 logs to API
2025-05-30 01:18:50 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:18:50 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:18:50 - utils.api_client - INFO - Async API client started
2025-05-30 01:18:50 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:18:50 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:18:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:18:50 - utils.api_client - INFO - API connection test successful
2025-05-30 01:18:50 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:18:50 - root - INFO - Event Log Collector initialized
2025-05-30 01:18:50 - root - INFO - Application Log Collector initialized
2025-05-30 01:18:50 - root - INFO - System Log Collector initialized
2025-05-30 01:18:50 - root - INFO - Network Log Collector initialized
2025-05-30 01:18:50 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:18:50 - root - INFO - Packet Collector initialized
2025-05-30 01:18:50 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:18:50 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:18:50 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:18:50 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:18:50 - root - INFO - Logging Agent started successfully
2025-05-30 01:18:50 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:18:50 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:18:50 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:18:50 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:18:50 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:18:50 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:18:50 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:18:50 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:18:50 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:18:50 - root - DEBUG - Memory usage for LoggingAgent: 85.17 MB
2025-05-30 01:18:55 - root - DEBUG - Queued 54 logs for API transmission
2025-05-30 01:18:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:18:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:18:55 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:18:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:18:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:18:55 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:18:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:18:55 - root - DEBUG - Collected 15 logs from network_logs
2025-05-30 01:18:55 - root - DEBUG - Total logs collected this cycle: 69
2025-05-30 01:18:55 - root - DEBUG - Memory usage for LoggingAgent: 85.38 MB
2025-05-30 01:19:00 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:19:00 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:19:00 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:19:00 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:19:00 - utils.api_client - WARNING - Sent 0/54 logs to API
2025-05-30 01:19:00 - root - DEBUG - Queued 69 logs for API transmission
2025-05-30 01:19:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:19:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:19:00 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:19:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:19:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:19:00 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:19:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:19:00 - root - DEBUG - Collected 8 logs from network_logs
2025-05-30 01:19:00 - root - DEBUG - Total logs collected this cycle: 62
2025-05-30 01:19:00 - root - DEBUG - Memory usage for LoggingAgent: 85.71 MB
2025-05-30 01:19:05 - root - DEBUG - Queued 62 logs for API transmission
2025-05-30 01:19:05 - root - INFO - Stopping Logging Agent...
2025-05-30 01:19:10 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:19:10 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:19:10 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:19:10 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:19:10 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:19:10 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:19:10 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:19:10 - utils.api_client - WARNING - Sent 0/131 logs to API
2025-05-30 01:19:10 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:19:11 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:19:11 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:19:11 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:19:48 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:19:48 - utils.api_client - INFO - Async API client started
2025-05-30 01:19:48 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:19:48 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:19:48 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:19:48 - utils.api_client - INFO - API connection test successful
2025-05-30 01:19:48 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:19:48 - root - INFO - Event Log Collector initialized
2025-05-30 01:19:48 - root - INFO - Application Log Collector initialized
2025-05-30 01:19:48 - root - INFO - System Log Collector initialized
2025-05-30 01:19:48 - root - INFO - Network Log Collector initialized
2025-05-30 01:19:48 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:19:48 - root - INFO - Packet Collector initialized
2025-05-30 01:19:48 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:19:48 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:19:48 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:19:48 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:19:48 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:19:48 - utils.api_client - INFO - API connection test successful
2025-05-30 01:19:49 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:19:49 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:19:49 - utils.api_client - INFO - Async API client started
2025-05-30 01:19:49 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:19:49 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:19:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:19:49 - utils.api_client - INFO - API connection test successful
2025-05-30 01:19:49 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:19:49 - root - INFO - Event Log Collector initialized
2025-05-30 01:19:49 - root - INFO - Application Log Collector initialized
2025-05-30 01:19:49 - root - INFO - System Log Collector initialized
2025-05-30 01:19:49 - root - INFO - Network Log Collector initialized
2025-05-30 01:19:49 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:19:49 - root - INFO - Packet Collector initialized
2025-05-30 01:19:49 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:19:49 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:19:49 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:19:49 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:19:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:19:49 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:19:49 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:19:49 - utils.api_client - WARNING - Sent 0/2 logs to API
2025-05-30 01:19:49 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:19:49 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:19:49 - utils.api_client - INFO - Async API client started
2025-05-30 01:19:49 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:19:49 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:19:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:19:49 - utils.api_client - INFO - API connection test successful
2025-05-30 01:19:49 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:19:49 - root - INFO - Event Log Collector initialized
2025-05-30 01:19:49 - root - INFO - Application Log Collector initialized
2025-05-30 01:19:49 - root - INFO - System Log Collector initialized
2025-05-30 01:19:49 - root - INFO - Network Log Collector initialized
2025-05-30 01:19:49 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:19:49 - root - INFO - Packet Collector initialized
2025-05-30 01:19:49 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:19:49 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:19:49 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:19:49 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:19:49 - root - INFO - Logging Agent started successfully
2025-05-30 01:19:49 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:19:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:19:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:19:49 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:19:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:19:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:19:49 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:19:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:19:49 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:19:49 - root - DEBUG - Memory usage for LoggingAgent: 85.51 MB
2025-05-30 01:19:54 - root - DEBUG - Queued 54 logs for API transmission
2025-05-30 01:19:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:19:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:19:54 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:19:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:19:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:19:54 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:19:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:19:54 - root - DEBUG - Collected 16 logs from network_logs
2025-05-30 01:19:54 - root - DEBUG - Total logs collected this cycle: 70
2025-05-30 01:19:54 - root - DEBUG - Memory usage for LoggingAgent: 86.35 MB
2025-05-30 01:19:59 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:19:59 - root - DEBUG - Queued 70 logs for API transmission
2025-05-30 01:19:59 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:19:59 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:19:59 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:19:59 - utils.api_client - WARNING - Sent 0/54 logs to API
2025-05-30 01:19:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:19:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:19:59 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:19:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:19:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:19:59 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:19:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:19:59 - root - DEBUG - Collected 13 logs from network_logs
2025-05-30 01:19:59 - root - DEBUG - Total logs collected this cycle: 67
2025-05-30 01:19:59 - root - DEBUG - Memory usage for LoggingAgent: 86.57 MB
2025-05-30 01:20:04 - root - DEBUG - Queued 67 logs for API transmission
2025-05-30 01:20:04 - root - INFO - Stopping Logging Agent...
2025-05-30 01:20:09 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:20:09 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:20:09 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:20:09 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:20:09 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:20:09 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:20:09 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:20:09 - utils.api_client - WARNING - Sent 0/137 logs to API
2025-05-30 01:20:09 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:20:10 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:20:10 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:20:10 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:20:24 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:20:24 - utils.api_client - INFO - Async API client started
2025-05-30 01:20:24 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:20:24 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:20:24 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:20:24 - utils.api_client - INFO - API connection test successful
2025-05-30 01:20:24 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:20:24 - root - INFO - Event Log Collector initialized
2025-05-30 01:20:24 - root - INFO - Application Log Collector initialized
2025-05-30 01:20:24 - root - INFO - System Log Collector initialized
2025-05-30 01:20:24 - root - INFO - Network Log Collector initialized
2025-05-30 01:20:24 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:20:24 - root - INFO - Packet Collector initialized
2025-05-30 01:20:24 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:20:24 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:20:24 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:20:24 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:20:24 - root - INFO - Logging Agent started successfully
2025-05-30 01:20:24 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:20:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:20:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:20:24 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:20:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:20:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:20:24 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:20:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:20:24 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:20:24 - root - DEBUG - Memory usage for LoggingAgent: 84.24 MB
2025-05-30 01:20:29 - root - DEBUG - Queued 54 logs for API transmission
2025-05-30 01:20:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:20:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:20:29 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:20:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:20:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:20:29 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:20:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:20:29 - root - DEBUG - Collected 13 logs from network_logs
2025-05-30 01:20:29 - root - DEBUG - Total logs collected this cycle: 67
2025-05-30 01:20:29 - root - DEBUG - Memory usage for LoggingAgent: 85.21 MB
2025-05-30 01:20:34 - root - DEBUG - Queued 67 logs for API transmission
2025-05-30 01:20:34 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:20:34 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:20:34 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:20:34 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:20:34 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:20:34 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:20:34 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:20:34 - utils.api_client - WARNING - Sent 0/121 logs to API
2025-05-30 01:20:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:20:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:20:34 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:20:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:20:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:20:34 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:20:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:20:34 - root - DEBUG - Collected 11 logs from network_logs
2025-05-30 01:20:34 - root - DEBUG - Total logs collected this cycle: 65
2025-05-30 01:20:34 - root - DEBUG - Memory usage for LoggingAgent: 85.32 MB
2025-05-30 01:20:39 - root - DEBUG - Queued 65 logs for API transmission
2025-05-30 01:20:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:20:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:20:39 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:20:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:20:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:20:39 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:20:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:20:39 - root - DEBUG - Collected 5 logs from network_logs
2025-05-30 01:20:39 - root - DEBUG - Total logs collected this cycle: 59
2025-05-30 01:20:39 - root - DEBUG - Memory usage for LoggingAgent: 85.35 MB
2025-05-30 01:20:40 - root - INFO - Stopping Logging Agent...
2025-05-30 01:20:44 - root - DEBUG - Queued 59 logs for API transmission
2025-05-30 01:20:44 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:20:44 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:20:44 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:20:44 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:20:44 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:20:44 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:20:44 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:20:44 - utils.api_client - WARNING - Sent 0/124 logs to API
2025-05-30 01:20:45 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:20:45 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:20:45 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:20:45 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:25:48 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:25:48 - utils.api_client - INFO - Async API client started
2025-05-30 01:25:48 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:25:48 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:25:48 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:25:48 - utils.api_client - INFO - API connection test successful
2025-05-30 01:25:48 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:25:48 - root - INFO - Event Log Collector initialized
2025-05-30 01:25:48 - root - INFO - Application Log Collector initialized
2025-05-30 01:25:48 - root - INFO - System Log Collector initialized
2025-05-30 01:25:48 - root - INFO - Network Log Collector initialized
2025-05-30 01:25:48 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:25:48 - root - INFO - Packet Collector initialized
2025-05-30 01:25:48 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:25:48 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:25:48 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:25:48 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:25:48 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 182
2025-05-30 01:25:48 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:25:48 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:25:48 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:25:48 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:25:48 - utils.api_client - INFO - Async API client started
2025-05-30 01:25:48 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:25:48 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:25:48 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:25:48 - utils.api_client - INFO - API connection test successful
2025-05-30 01:25:48 - root - INFO - API connection test successful (response time: 0.04s)
2025-05-30 01:25:48 - root - INFO - Event Log Collector initialized
2025-05-30 01:25:48 - root - INFO - Application Log Collector initialized
2025-05-30 01:25:48 - root - INFO - System Log Collector initialized
2025-05-30 01:25:49 - root - INFO - Network Log Collector initialized
2025-05-30 01:25:49 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:25:49 - root - INFO - Packet Collector initialized
2025-05-30 01:25:49 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:25:49 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:25:49 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:25:49 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:25:49 - root - INFO - Logging Agent started successfully
2025-05-30 01:25:49 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:25:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:25:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:25:49 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:25:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:25:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:25:49 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:25:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:25:49 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:25:49 - root - DEBUG - Memory usage for LoggingAgent: 84.54 MB
2025-05-30 01:26:02 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:26:02 - utils.api_client - INFO - Async API client started
2025-05-30 01:26:02 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:26:02 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:26:02 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:26:02 - utils.api_client - INFO - API connection test successful
2025-05-30 01:26:02 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:26:02 - root - INFO - Event Log Collector initialized
2025-05-30 01:26:02 - root - INFO - Application Log Collector initialized
2025-05-30 01:26:02 - root - INFO - System Log Collector initialized
2025-05-30 01:26:02 - root - INFO - Network Log Collector initialized
2025-05-30 01:26:02 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:26:02 - root - INFO - Packet Collector initialized
2025-05-30 01:26:02 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:26:02 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:26:02 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:26:02 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:26:02 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:26:02 - utils.api_client - INFO - API connection test successful
2025-05-30 01:26:03 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:26:03 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:26:03 - utils.api_client - INFO - Async API client started
2025-05-30 01:26:03 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:26:03 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:26:03 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:26:03 - utils.api_client - INFO - API connection test successful
2025-05-30 01:26:03 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:26:03 - root - INFO - Event Log Collector initialized
2025-05-30 01:26:03 - root - INFO - Application Log Collector initialized
2025-05-30 01:26:03 - root - INFO - System Log Collector initialized
2025-05-30 01:26:03 - root - INFO - Network Log Collector initialized
2025-05-30 01:26:03 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:26:03 - root - INFO - Packet Collector initialized
2025-05-30 01:26:03 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:26:03 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:26:03 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:26:03 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:26:03 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:26:03 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:26:03 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:26:03 - utils.api_client - WARNING - Sent 0/2 logs to API
2025-05-30 01:26:03 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:26:03 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:26:03 - utils.api_client - INFO - Async API client started
2025-05-30 01:26:03 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:26:03 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:26:03 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:26:03 - utils.api_client - INFO - API connection test successful
2025-05-30 01:26:03 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:26:03 - root - INFO - Event Log Collector initialized
2025-05-30 01:26:03 - root - INFO - Application Log Collector initialized
2025-05-30 01:26:03 - root - INFO - System Log Collector initialized
2025-05-30 01:26:03 - root - INFO - Network Log Collector initialized
2025-05-30 01:26:03 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:26:03 - root - INFO - Packet Collector initialized
2025-05-30 01:26:03 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:26:03 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:26:03 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:26:03 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:26:03 - root - INFO - Logging Agent started successfully
2025-05-30 01:26:03 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:26:03 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:26:03 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:26:03 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:26:03 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:26:03 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:26:03 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:26:03 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:26:03 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:26:03 - root - DEBUG - Memory usage for LoggingAgent: 85.66 MB
2025-05-30 01:26:08 - root - DEBUG - Queued 54 logs for API transmission
2025-05-30 01:26:08 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:26:08 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:26:08 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:26:08 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:26:08 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:26:08 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:26:08 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:26:08 - root - DEBUG - Collected 13 logs from network_logs
2025-05-30 01:26:08 - root - DEBUG - Total logs collected this cycle: 67
2025-05-30 01:26:08 - root - DEBUG - Memory usage for LoggingAgent: 86.63 MB
2025-05-30 01:26:13 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:26:13 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:26:13 - root - DEBUG - Queued 67 logs for API transmission
2025-05-30 01:26:13 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:26:13 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:26:13 - utils.api_client - WARNING - Sent 0/54 logs to API
2025-05-30 01:26:13 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:26:13 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:26:13 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:26:13 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:26:13 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:26:13 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:26:13 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:26:13 - root - DEBUG - Collected 6 logs from network_logs
2025-05-30 01:26:13 - root - DEBUG - Total logs collected this cycle: 60
2025-05-30 01:26:13 - root - DEBUG - Memory usage for LoggingAgent: 86.81 MB
2025-05-30 01:26:18 - root - DEBUG - Queued 60 logs for API transmission
2025-05-30 01:26:18 - root - INFO - Stopping Logging Agent...
2025-05-30 01:26:23 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:26:23 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:26:23 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:26:23 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:26:23 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:26:23 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:26:23 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:26:23 - utils.api_client - WARNING - Sent 0/127 logs to API
2025-05-30 01:26:23 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:26:24 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:26:24 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:26:24 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:27:27 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:27:27 - utils.api_client - INFO - Async API client started
2025-05-30 01:27:27 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:27:27 - utils.api_client - INFO - API connection test successful
2025-05-30 01:27:27 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:27:27 - root - INFO - Event Log Collector initialized
2025-05-30 01:27:27 - root - INFO - Application Log Collector initialized
2025-05-30 01:27:27 - root - INFO - System Log Collector initialized
2025-05-30 01:27:27 - root - INFO - Network Log Collector initialized
2025-05-30 01:27:27 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:27:27 - root - INFO - Packet Collector initialized
2025-05-30 01:27:27 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:27:27 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:27:27 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:27:27 - utils.api_client - INFO - API connection test successful
2025-05-30 01:27:27 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:27:27 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:27:27 - utils.api_client - INFO - Async API client started
2025-05-30 01:27:27 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:27:27 - utils.api_client - INFO - API connection test successful
2025-05-30 01:27:27 - root - INFO - API connection test successful (response time: 0.04s)
2025-05-30 01:27:27 - root - INFO - Event Log Collector initialized
2025-05-30 01:27:27 - root - INFO - Application Log Collector initialized
2025-05-30 01:27:27 - root - INFO - System Log Collector initialized
2025-05-30 01:27:27 - root - INFO - Network Log Collector initialized
2025-05-30 01:27:27 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:27:27 - root - INFO - Packet Collector initialized
2025-05-30 01:27:27 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:27:27 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:27:27 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:27:27 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:27:27 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:27:27 - utils.api_client - WARNING - Sent 0/2 logs to API
2025-05-30 01:27:27 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:27:27 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:27:27 - utils.api_client - INFO - Async API client started
2025-05-30 01:27:27 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:27:27 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:27:27 - utils.api_client - INFO - API connection test successful
2025-05-30 01:27:27 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:27:27 - root - INFO - Event Log Collector initialized
2025-05-30 01:27:27 - root - INFO - Application Log Collector initialized
2025-05-30 01:27:27 - root - INFO - System Log Collector initialized
2025-05-30 01:27:27 - root - INFO - Network Log Collector initialized
2025-05-30 01:27:27 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:27:27 - root - INFO - Packet Collector initialized
2025-05-30 01:27:27 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:27:27 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:27:27 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:27:27 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:27:27 - root - INFO - Logging Agent started successfully
2025-05-30 01:27:27 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:27:27 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:27:27 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:27:27 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:27:27 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:27:27 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:27:27 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:27:27 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:27:28 - root - DEBUG - Total logs collected this cycle: 54
2025-05-30 01:27:28 - root - DEBUG - Memory usage for LoggingAgent: 85.46 MB
2025-05-30 01:27:32 - root - DEBUG - Queued 54 logs for API transmission
2025-05-30 01:27:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:27:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:27:33 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:27:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:27:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:27:33 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:27:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:27:33 - root - DEBUG - Collected 15 logs from network_logs
2025-05-30 01:27:33 - root - DEBUG - Total logs collected this cycle: 69
2025-05-30 01:27:33 - root - DEBUG - Memory usage for LoggingAgent: 85.62 MB
2025-05-30 01:27:37 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:27:37 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:27:37 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:27:37 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:27:37 - utils.api_client - WARNING - Sent 0/54 logs to API
2025-05-30 01:27:37 - root - DEBUG - Queued 69 logs for API transmission
2025-05-30 01:27:37 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:27:38 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:27:38 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:27:38 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:27:38 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:27:38 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:27:38 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:27:38 - root - DEBUG - Collected 11 logs from network_logs
2025-05-30 01:27:38 - root - DEBUG - Total logs collected this cycle: 65
2025-05-30 01:27:38 - root - DEBUG - Memory usage for LoggingAgent: 85.92 MB
2025-05-30 01:27:42 - root - DEBUG - Queued 65 logs for API transmission
2025-05-30 01:27:42 - root - INFO - Stopping Logging Agent...
2025-05-30 01:27:47 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:27:47 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:27:47 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:27:47 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:27:47 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:27:47 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:27:47 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:27:47 - utils.api_client - WARNING - Sent 0/134 logs to API
2025-05-30 01:27:47 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:27:48 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:27:48 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:27:48 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:29:03 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:29:03 - utils.api_client - INFO - Async API client started
2025-05-30 01:29:03 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:29:03 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:29:03 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:29:03 - utils.api_client - INFO - API connection test successful
2025-05-30 01:29:03 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:29:03 - root - INFO - Event Log Collector initialized
2025-05-30 01:29:03 - root - INFO - Application Log Collector initialized
2025-05-30 01:29:03 - root - INFO - System Log Collector initialized
2025-05-30 01:29:03 - root - INFO - Network Log Collector initialized
2025-05-30 01:29:03 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:29:03 - root - INFO - Packet Collector initialized
2025-05-30 01:29:03 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:29:03 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:29:03 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:29:03 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:29:03 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:29:03 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:29:03 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:29:03 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:29:03 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:29:03 - utils.api_client - INFO - Async API client started
2025-05-30 01:29:03 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:29:03 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:29:03 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:29:03 - utils.api_client - INFO - API connection test successful
2025-05-30 01:29:03 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:29:03 - root - INFO - Event Log Collector initialized
2025-05-30 01:29:03 - root - INFO - Application Log Collector initialized
2025-05-30 01:29:03 - root - INFO - System Log Collector initialized
2025-05-30 01:29:04 - root - INFO - Network Log Collector initialized
2025-05-30 01:29:04 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:29:04 - root - INFO - Packet Collector initialized
2025-05-30 01:29:04 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:29:04 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:29:04 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:29:04 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:29:04 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:29:04 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:29:04 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:29:16 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:29:16 - utils.api_client - INFO - Async API client started
2025-05-30 01:29:16 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:29:16 - utils.api_client - INFO - API connection test successful
2025-05-30 01:29:16 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:29:16 - root - INFO - Event Log Collector initialized
2025-05-30 01:29:16 - root - INFO - Application Log Collector initialized
2025-05-30 01:29:16 - root - INFO - System Log Collector initialized
2025-05-30 01:29:16 - root - INFO - Network Log Collector initialized
2025-05-30 01:29:16 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:29:16 - root - INFO - Packet Collector initialized
2025-05-30 01:29:16 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:29:16 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:29:16 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:29:16 - utils.api_client - INFO - API connection test successful
2025-05-30 01:29:16 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:29:16 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:29:16 - utils.api_client - INFO - Async API client started
2025-05-30 01:29:16 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:29:16 - utils.api_client - INFO - API connection test successful
2025-05-30 01:29:16 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:29:16 - root - INFO - Event Log Collector initialized
2025-05-30 01:29:16 - root - INFO - Application Log Collector initialized
2025-05-30 01:29:16 - root - INFO - System Log Collector initialized
2025-05-30 01:29:16 - root - INFO - Network Log Collector initialized
2025-05-30 01:29:16 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:29:16 - root - INFO - Packet Collector initialized
2025-05-30 01:29:16 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:29:16 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:29:16 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 229
2025-05-30 01:29:16 - utils.api_client - DEBUG - Successfully sent batch of 2 logs
2025-05-30 01:29:16 - utils.api_client - DEBUG - Successfully sent 2 logs to API
2025-05-30 01:29:16 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:29:16 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:29:16 - utils.api_client - INFO - Async API client started
2025-05-30 01:29:16 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:29:16 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:29:16 - utils.api_client - INFO - API connection test successful
2025-05-30 01:29:16 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:29:16 - root - INFO - Event Log Collector initialized
2025-05-30 01:29:16 - root - INFO - Application Log Collector initialized
2025-05-30 01:29:16 - root - INFO - System Log Collector initialized
2025-05-30 01:29:16 - root - INFO - Network Log Collector initialized
2025-05-30 01:29:16 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:29:16 - root - INFO - Packet Collector initialized
2025-05-30 01:29:16 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:29:16 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:29:16 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:29:16 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:29:16 - root - INFO - Logging Agent started successfully
2025-05-30 01:29:16 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:29:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:29:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:29:16 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:29:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:29:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:29:16 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:29:16 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:29:16 - root - DEBUG - Collected 1 logs from network_logs
2025-05-30 01:29:16 - root - DEBUG - Total logs collected this cycle: 55
2025-05-30 01:29:16 - root - DEBUG - Memory usage for LoggingAgent: 85.66 MB
2025-05-30 01:29:21 - root - DEBUG - Queued 55 logs for API transmission
2025-05-30 01:29:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:29:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:29:21 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:29:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:29:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:29:21 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:29:21 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:29:21 - root - DEBUG - Collected 15 logs from network_logs
2025-05-30 01:29:21 - root - DEBUG - Total logs collected this cycle: 69
2025-05-30 01:29:21 - root - DEBUG - Memory usage for LoggingAgent: 86.73 MB
2025-05-30 01:29:26 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:29:26 - root - DEBUG - Queued 69 logs for API transmission
2025-05-30 01:29:26 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:29:26 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:29:26 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:29:26 - utils.api_client - WARNING - Sent 0/55 logs to API
2025-05-30 01:29:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:29:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:29:26 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:29:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:29:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:29:26 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:29:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:29:26 - root - DEBUG - Collected 6 logs from network_logs
2025-05-30 01:29:26 - root - DEBUG - Total logs collected this cycle: 60
2025-05-30 01:29:26 - root - DEBUG - Memory usage for LoggingAgent: 86.94 MB
2025-05-30 01:29:31 - root - DEBUG - Queued 60 logs for API transmission
2025-05-30 01:29:31 - root - INFO - Stopping Logging Agent...
2025-05-30 01:29:36 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:29:36 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:29:36 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:29:36 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:29:36 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:29:36 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:29:36 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:29:36 - utils.api_client - WARNING - Sent 0/129 logs to API
2025-05-30 01:29:36 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:29:37 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:29:37 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:29:37 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:30:43 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:30:43 - utils.api_client - INFO - Async API client started
2025-05-30 01:30:43 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:30:43 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:30:43 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:30:43 - utils.api_client - INFO - API connection test successful
2025-05-30 01:30:43 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:30:43 - root - INFO - Event Log Collector initialized
2025-05-30 01:30:43 - root - INFO - Application Log Collector initialized
2025-05-30 01:30:43 - root - INFO - System Log Collector initialized
2025-05-30 01:30:43 - root - INFO - Network Log Collector initialized
2025-05-30 01:30:43 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:30:43 - root - INFO - Packet Collector initialized
2025-05-30 01:30:43 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:30:43 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:30:43 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:30:43 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:30:43 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:30:43 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:30:43 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:30:43 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:30:43 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:30:43 - utils.api_client - INFO - Async API client started
2025-05-30 01:30:43 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:30:43 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:30:43 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:30:43 - utils.api_client - INFO - API connection test successful
2025-05-30 01:30:43 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:30:43 - root - INFO - Event Log Collector initialized
2025-05-30 01:30:43 - root - INFO - Application Log Collector initialized
2025-05-30 01:30:43 - root - INFO - System Log Collector initialized
2025-05-30 01:30:43 - root - INFO - Network Log Collector initialized
2025-05-30 01:30:43 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:30:43 - root - INFO - Packet Collector initialized
2025-05-30 01:30:43 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:30:43 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:30:43 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:30:43 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:30:43 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:30:43 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:30:43 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:30:51 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:30:51 - utils.api_client - INFO - Async API client started
2025-05-30 01:30:51 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:30:51 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:30:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:30:51 - utils.api_client - INFO - API connection test successful
2025-05-30 01:30:51 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:30:51 - root - INFO - Event Log Collector initialized
2025-05-30 01:30:51 - root - INFO - Application Log Collector initialized
2025-05-30 01:30:51 - root - INFO - System Log Collector initialized
2025-05-30 01:30:51 - root - INFO - Network Log Collector initialized
2025-05-30 01:30:51 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:30:51 - root - INFO - Packet Collector initialized
2025-05-30 01:30:51 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:30:51 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:30:51 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:30:51 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:30:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:30:52 - utils.api_client - INFO - API connection test successful
2025-05-30 01:30:52 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:30:52 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:30:52 - utils.api_client - INFO - Async API client started
2025-05-30 01:30:52 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:30:52 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:30:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:30:52 - utils.api_client - INFO - API connection test successful
2025-05-30 01:30:52 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:30:52 - root - INFO - Event Log Collector initialized
2025-05-30 01:30:52 - root - INFO - Application Log Collector initialized
2025-05-30 01:30:52 - root - INFO - System Log Collector initialized
2025-05-30 01:30:52 - root - INFO - Network Log Collector initialized
2025-05-30 01:30:52 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:30:52 - root - INFO - Packet Collector initialized
2025-05-30 01:30:52 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:30:52 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:30:52 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:30:52 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:30:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 229
2025-05-30 01:30:52 - utils.api_client - DEBUG - Successfully sent batch of 2 logs
2025-05-30 01:30:52 - utils.api_client - DEBUG - Successfully sent 2 logs to API
2025-05-30 01:30:52 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:30:52 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:30:52 - utils.api_client - INFO - Async API client started
2025-05-30 01:30:52 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:30:52 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:30:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:30:52 - utils.api_client - INFO - API connection test successful
2025-05-30 01:30:52 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:30:52 - root - INFO - Event Log Collector initialized
2025-05-30 01:30:52 - root - INFO - Application Log Collector initialized
2025-05-30 01:30:52 - root - INFO - System Log Collector initialized
2025-05-30 01:30:52 - root - INFO - Network Log Collector initialized
2025-05-30 01:30:52 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:30:52 - root - INFO - Packet Collector initialized
2025-05-30 01:30:52 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:30:52 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:30:52 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:30:52 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:30:52 - root - INFO - Logging Agent started successfully
2025-05-30 01:30:52 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:30:52 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:30:52 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:30:52 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:30:52 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:30:52 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:30:52 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:30:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:30:54 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:30:54 - root - DEBUG - Collected 7 logs from network_logs
2025-05-30 01:30:54 - root - DEBUG - Total logs collected this cycle: 62
2025-05-30 01:30:54 - root - DEBUG - Memory usage for LoggingAgent: 92.63 MB
2025-05-30 01:30:57 - root - DEBUG - Queued 62 logs for API transmission
2025-05-30 01:30:57 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:30:57 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:30:57 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:30:57 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:30:57 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:30:57 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:30:58 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:30:58 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:30:58 - root - DEBUG - Collected 13 logs from network_logs
2025-05-30 01:30:58 - root - DEBUG - Total logs collected this cycle: 68
2025-05-30 01:30:58 - root - DEBUG - Memory usage for LoggingAgent: 92.79 MB
2025-05-30 01:31:02 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:31:02 - root - DEBUG - Queued 68 logs for API transmission
2025-05-30 01:31:02 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:31:02 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:31:02 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:31:02 - utils.api_client - WARNING - Sent 0/62 logs to API
2025-05-30 01:31:02 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:31:02 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:31:02 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:31:02 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:31:02 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:31:02 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:31:03 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:31:03 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:31:03 - root - DEBUG - Collected 17 logs from network_logs
2025-05-30 01:31:03 - root - DEBUG - Total logs collected this cycle: 72
2025-05-30 01:31:03 - root - DEBUG - Memory usage for LoggingAgent: 92.96 MB
2025-05-30 01:31:07 - root - DEBUG - Queued 72 logs for API transmission
2025-05-30 01:31:07 - root - INFO - Stopping Logging Agent...
2025-05-30 01:31:12 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:31:12 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:31:12 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:31:12 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:31:12 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:31:12 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:31:12 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:31:12 - utils.api_client - WARNING - Sent 0/140 logs to API
2025-05-30 01:31:12 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:31:13 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:31:13 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:31:13 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:33:17 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:33:17 - utils.api_client - INFO - Async API client started
2025-05-30 01:33:17 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:33:17 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:33:17 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:33:17 - utils.api_client - INFO - API connection test successful
2025-05-30 01:33:17 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:33:17 - root - INFO - Event Log Collector initialized
2025-05-30 01:33:17 - root - INFO - Application Log Collector initialized
2025-05-30 01:33:17 - root - INFO - System Log Collector initialized
2025-05-30 01:33:17 - root - INFO - Network Log Collector initialized
2025-05-30 01:33:17 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:33:17 - root - INFO - Packet Collector initialized
2025-05-30 01:33:17 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:33:17 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:33:17 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:33:17 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:33:17 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:33:17 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:33:17 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:33:17 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:33:17 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:33:17 - utils.api_client - INFO - Async API client started
2025-05-30 01:33:17 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:33:17 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:33:17 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:33:17 - utils.api_client - INFO - API connection test successful
2025-05-30 01:33:17 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:33:17 - root - INFO - Event Log Collector initialized
2025-05-30 01:33:17 - root - INFO - Application Log Collector initialized
2025-05-30 01:33:17 - root - INFO - System Log Collector initialized
2025-05-30 01:33:17 - root - INFO - Network Log Collector initialized
2025-05-30 01:33:17 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:33:17 - root - INFO - Packet Collector initialized
2025-05-30 01:33:17 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:33:17 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:33:17 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:33:17 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:33:17 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:33:17 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:33:17 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:33:26 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:33:26 - utils.api_client - INFO - Async API client started
2025-05-30 01:33:26 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:33:26 - utils.api_client - INFO - API connection test successful
2025-05-30 01:33:26 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:33:26 - root - INFO - Event Log Collector initialized
2025-05-30 01:33:26 - root - INFO - Application Log Collector initialized
2025-05-30 01:33:26 - root - INFO - System Log Collector initialized
2025-05-30 01:33:26 - root - INFO - Network Log Collector initialized
2025-05-30 01:33:26 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:33:26 - root - INFO - Packet Collector initialized
2025-05-30 01:33:26 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:33:26 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:33:26 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:33:26 - utils.api_client - INFO - API connection test successful
2025-05-30 01:33:26 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:33:26 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:33:26 - utils.api_client - INFO - Async API client started
2025-05-30 01:33:26 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:33:26 - utils.api_client - INFO - API connection test successful
2025-05-30 01:33:26 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:33:26 - root - INFO - Event Log Collector initialized
2025-05-30 01:33:26 - root - INFO - Application Log Collector initialized
2025-05-30 01:33:26 - root - INFO - System Log Collector initialized
2025-05-30 01:33:26 - root - INFO - Network Log Collector initialized
2025-05-30 01:33:26 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:33:26 - root - INFO - Packet Collector initialized
2025-05-30 01:33:26 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:33:26 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:33:26 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 229
2025-05-30 01:33:26 - utils.api_client - DEBUG - Successfully sent batch of 2 logs
2025-05-30 01:33:26 - utils.api_client - DEBUG - Successfully sent 2 logs to API
2025-05-30 01:33:26 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:33:26 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:33:26 - utils.api_client - INFO - Async API client started
2025-05-30 01:33:26 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:33:26 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:33:26 - utils.api_client - INFO - API connection test successful
2025-05-30 01:33:26 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:33:26 - root - INFO - Event Log Collector initialized
2025-05-30 01:33:26 - root - INFO - Application Log Collector initialized
2025-05-30 01:33:26 - root - INFO - System Log Collector initialized
2025-05-30 01:33:26 - root - INFO - Network Log Collector initialized
2025-05-30 01:33:26 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:33:26 - root - INFO - Packet Collector initialized
2025-05-30 01:33:26 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:33:26 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:33:26 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:33:26 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:33:26 - root - INFO - Logging Agent started successfully
2025-05-30 01:33:26 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:33:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:33:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:33:26 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:33:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:33:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:33:26 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:33:26 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:33:26 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:33:26 - root - DEBUG - Collected 1 logs from network_logs
2025-05-30 01:33:26 - root - DEBUG - Total logs collected this cycle: 56
2025-05-30 01:33:26 - root - DEBUG - Memory usage for LoggingAgent: 85.38 MB
2025-05-30 01:33:31 - root - DEBUG - Queued 56 logs for API transmission
2025-05-30 01:33:31 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:33:31 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:33:31 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:33:31 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:33:31 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:33:31 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:33:31 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:33:31 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:33:31 - root - DEBUG - Collected 17 logs from network_logs
2025-05-30 01:33:31 - root - DEBUG - Total logs collected this cycle: 72
2025-05-30 01:33:31 - root - DEBUG - Memory usage for LoggingAgent: 86.11 MB
2025-05-30 01:33:36 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:33:36 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:33:36 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:33:36 - root - DEBUG - Queued 72 logs for API transmission
2025-05-30 01:33:36 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:33:36 - utils.api_client - WARNING - Sent 0/56 logs to API
2025-05-30 01:33:36 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:33:36 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:33:36 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:33:36 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:33:36 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:33:36 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:33:36 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:33:36 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:33:36 - root - DEBUG - Collected 10 logs from network_logs
2025-05-30 01:33:36 - root - DEBUG - Total logs collected this cycle: 65
2025-05-30 01:33:36 - root - DEBUG - Memory usage for LoggingAgent: 86.26 MB
2025-05-30 01:33:41 - root - DEBUG - Queued 65 logs for API transmission
2025-05-30 01:33:41 - root - INFO - Stopping Logging Agent...
2025-05-30 01:33:46 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:33:46 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:33:46 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:33:46 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:33:46 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:33:46 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:33:46 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:33:46 - utils.api_client - WARNING - Sent 0/137 logs to API
2025-05-30 01:33:46 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:33:47 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:33:47 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:33:47 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:34:42 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:34:42 - utils.api_client - INFO - Async API client started
2025-05-30 01:34:42 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:34:42 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:34:42 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:34:42 - utils.api_client - INFO - API connection test successful
2025-05-30 01:34:42 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:34:42 - root - INFO - Event Log Collector initialized
2025-05-30 01:34:42 - root - INFO - Application Log Collector initialized
2025-05-30 01:34:42 - root - INFO - System Log Collector initialized
2025-05-30 01:34:42 - root - INFO - Network Log Collector initialized
2025-05-30 01:34:42 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:34:42 - root - INFO - Packet Collector initialized
2025-05-30 01:34:42 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:34:42 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:34:42 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:34:42 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:34:42 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:34:42 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:34:42 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:34:42 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:34:42 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:34:42 - utils.api_client - INFO - Async API client started
2025-05-30 01:34:42 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:34:42 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:34:42 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:34:42 - utils.api_client - INFO - API connection test successful
2025-05-30 01:34:42 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:34:42 - root - INFO - Event Log Collector initialized
2025-05-30 01:34:42 - root - INFO - Application Log Collector initialized
2025-05-30 01:34:42 - root - INFO - System Log Collector initialized
2025-05-30 01:34:42 - root - INFO - Network Log Collector initialized
2025-05-30 01:34:42 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:34:42 - root - INFO - Packet Collector initialized
2025-05-30 01:34:42 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:34:42 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:34:42 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:34:42 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:34:42 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:34:42 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:34:42 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:34:54 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:34:54 - utils.api_client - INFO - Async API client started
2025-05-30 01:34:54 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:34:54 - utils.api_client - INFO - API connection test successful
2025-05-30 01:34:54 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:34:54 - root - INFO - Event Log Collector initialized
2025-05-30 01:34:54 - root - INFO - Application Log Collector initialized
2025-05-30 01:34:54 - root - INFO - System Log Collector initialized
2025-05-30 01:34:54 - root - INFO - Network Log Collector initialized
2025-05-30 01:34:54 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:34:54 - root - INFO - Packet Collector initialized
2025-05-30 01:34:54 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:34:54 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:34:54 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:34:54 - utils.api_client - INFO - API connection test successful
2025-05-30 01:34:54 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:34:54 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:34:54 - utils.api_client - INFO - Async API client started
2025-05-30 01:34:54 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:34:54 - utils.api_client - INFO - API connection test successful
2025-05-30 01:34:54 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:34:54 - root - INFO - Event Log Collector initialized
2025-05-30 01:34:54 - root - INFO - Application Log Collector initialized
2025-05-30 01:34:54 - root - INFO - System Log Collector initialized
2025-05-30 01:34:54 - root - INFO - Network Log Collector initialized
2025-05-30 01:34:54 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:34:54 - root - INFO - Packet Collector initialized
2025-05-30 01:34:54 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:34:54 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:34:54 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 229
2025-05-30 01:34:54 - utils.api_client - DEBUG - Successfully sent batch of 2 logs
2025-05-30 01:34:54 - utils.api_client - DEBUG - Successfully sent 2 logs to API
2025-05-30 01:34:54 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:34:54 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:34:54 - utils.api_client - INFO - Async API client started
2025-05-30 01:34:54 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:34:54 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:34:55 - utils.api_client - INFO - API connection test successful
2025-05-30 01:34:55 - root - INFO - API connection test successful (response time: 0.06s)
2025-05-30 01:34:55 - root - INFO - Event Log Collector initialized
2025-05-30 01:34:55 - root - INFO - Application Log Collector initialized
2025-05-30 01:34:55 - root - INFO - System Log Collector initialized
2025-05-30 01:34:55 - root - INFO - Network Log Collector initialized
2025-05-30 01:34:55 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:34:55 - root - INFO - Packet Collector initialized
2025-05-30 01:34:55 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:34:55 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:34:55 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:34:55 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:34:55 - root - INFO - Logging Agent started successfully
2025-05-30 01:34:55 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:34:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:34:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:34:55 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:34:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:34:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:34:55 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:34:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:34:55 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:34:55 - root - DEBUG - Collected 1 logs from network_logs
2025-05-30 01:34:55 - root - DEBUG - Total logs collected this cycle: 56
2025-05-30 01:34:55 - root - DEBUG - Memory usage for LoggingAgent: 85.38 MB
2025-05-30 01:34:59 - root - DEBUG - Queued 56 logs for API transmission
2025-05-30 01:35:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:35:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:35:00 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:35:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:35:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:35:00 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:35:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:35:00 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:35:00 - root - DEBUG - Collected 12 logs from network_logs
2025-05-30 01:35:00 - root - DEBUG - Total logs collected this cycle: 67
2025-05-30 01:35:00 - root - DEBUG - Memory usage for LoggingAgent: 85.60 MB
2025-05-30 01:35:04 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:35:04 - root - DEBUG - Queued 67 logs for API transmission
2025-05-30 01:35:04 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:35:04 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:35:04 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:35:04 - utils.api_client - WARNING - Sent 0/56 logs to API
2025-05-30 01:35:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 10 logs from System
2025-05-30 01:35:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:35:05 - root - DEBUG - Collected 29 logs from event_logs
2025-05-30 01:35:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:35:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:35:05 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:35:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:35:05 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:35:05 - root - DEBUG - Collected 10 logs from network_logs
2025-05-30 01:35:05 - root - DEBUG - Total logs collected this cycle: 65
2025-05-30 01:35:05 - root - DEBUG - Memory usage for LoggingAgent: 85.78 MB
2025-05-30 01:35:09 - root - DEBUG - Queued 65 logs for API transmission
2025-05-30 01:35:10 - root - INFO - Stopping Logging Agent...
2025-05-30 01:35:14 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:35:15 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:35:15 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:35:15 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:35:15 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:35:15 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:35:15 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:35:15 - utils.api_client - WARNING - Sent 0/132 logs to API
2025-05-30 01:35:15 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:35:15 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:35:15 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:35:15 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:36:16 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:36:16 - utils.api_client - INFO - Async API client started
2025-05-30 01:36:16 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:36:16 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:36:16 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:36:16 - utils.api_client - INFO - API connection test successful
2025-05-30 01:36:16 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:36:16 - root - INFO - Event Log Collector initialized
2025-05-30 01:36:16 - root - INFO - Application Log Collector initialized
2025-05-30 01:36:16 - root - INFO - System Log Collector initialized
2025-05-30 01:36:16 - root - INFO - Network Log Collector initialized
2025-05-30 01:36:16 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:36:16 - root - INFO - Packet Collector initialized
2025-05-30 01:36:16 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:36:16 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:36:16 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:36:16 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:36:16 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:36:16 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:36:16 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:36:16 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:36:16 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:36:16 - utils.api_client - INFO - Async API client started
2025-05-30 01:36:16 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:36:16 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:36:16 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:36:16 - utils.api_client - INFO - API connection test successful
2025-05-30 01:36:16 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:36:16 - root - INFO - Event Log Collector initialized
2025-05-30 01:36:16 - root - INFO - Application Log Collector initialized
2025-05-30 01:36:16 - root - INFO - System Log Collector initialized
2025-05-30 01:36:16 - root - INFO - Network Log Collector initialized
2025-05-30 01:36:16 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:36:16 - root - INFO - Packet Collector initialized
2025-05-30 01:36:16 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:36:16 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:36:16 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:36:16 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:36:16 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:36:16 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:36:16 - utils.api_client - DEBUG - Successfully sent 1 logs to API
2025-05-30 01:36:33 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:36:33 - utils.api_client - INFO - Async API client started
2025-05-30 01:36:33 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:36:33 - utils.api_client - INFO - API connection test successful
2025-05-30 01:36:33 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:36:33 - root - INFO - Event Log Collector initialized
2025-05-30 01:36:33 - root - INFO - Application Log Collector initialized
2025-05-30 01:36:33 - root - INFO - System Log Collector initialized
2025-05-30 01:36:33 - root - INFO - Network Log Collector initialized
2025-05-30 01:36:33 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:36:33 - root - INFO - Packet Collector initialized
2025-05-30 01:36:33 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:36:33 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:36:33 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:36:33 - utils.api_client - INFO - API connection test successful
2025-05-30 01:36:33 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:36:33 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:36:33 - utils.api_client - INFO - Async API client started
2025-05-30 01:36:33 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:36:33 - utils.api_client - INFO - API connection test successful
2025-05-30 01:36:33 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:36:33 - root - INFO - Event Log Collector initialized
2025-05-30 01:36:33 - root - INFO - Application Log Collector initialized
2025-05-30 01:36:33 - root - INFO - System Log Collector initialized
2025-05-30 01:36:33 - root - INFO - Network Log Collector initialized
2025-05-30 01:36:33 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:36:33 - root - INFO - Packet Collector initialized
2025-05-30 01:36:33 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:36:33 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:36:33 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 229
2025-05-30 01:36:33 - utils.api_client - DEBUG - Successfully sent batch of 2 logs
2025-05-30 01:36:33 - utils.api_client - DEBUG - Successfully sent 2 logs to API
2025-05-30 01:36:33 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:36:33 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:36:33 - utils.api_client - INFO - Async API client started
2025-05-30 01:36:33 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:36:33 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:36:33 - utils.api_client - INFO - API connection test successful
2025-05-30 01:36:33 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:36:33 - root - INFO - Event Log Collector initialized
2025-05-30 01:36:33 - root - INFO - Application Log Collector initialized
2025-05-30 01:36:33 - root - INFO - System Log Collector initialized
2025-05-30 01:36:33 - root - INFO - Network Log Collector initialized
2025-05-30 01:36:33 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:36:33 - root - INFO - Packet Collector initialized
2025-05-30 01:36:33 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:36:33 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:36:33 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:36:33 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:36:33 - root - INFO - Logging Agent started successfully
2025-05-30 01:36:33 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:36:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:36:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:36:33 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:36:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:36:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:36:33 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:36:33 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:36:33 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:36:33 - root - DEBUG - Collected 1 logs from network_logs
2025-05-30 01:36:33 - root - DEBUG - Total logs collected this cycle: 57
2025-05-30 01:36:33 - root - DEBUG - Memory usage for LoggingAgent: 85.57 MB
2025-05-30 01:36:38 - root - DEBUG - Queued 57 logs for API transmission
2025-05-30 01:36:38 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:36:38 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:36:38 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:36:38 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:36:38 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:36:38 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:36:38 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:36:38 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:36:38 - root - DEBUG - Collected 12 logs from network_logs
2025-05-30 01:36:38 - root - DEBUG - Total logs collected this cycle: 68
2025-05-30 01:36:38 - root - DEBUG - Memory usage for LoggingAgent: 86.42 MB
2025-05-30 01:36:43 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:36:43 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:36:43 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:36:43 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:36:43 - root - DEBUG - Queued 68 logs for API transmission
2025-05-30 01:36:43 - utils.api_client - WARNING - Sent 0/57 logs to API
2025-05-30 01:36:43 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:36:43 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:36:43 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:36:43 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:36:43 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:36:43 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:36:43 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:36:43 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:36:43 - root - DEBUG - Collected 6 logs from network_logs
2025-05-30 01:36:43 - root - DEBUG - Total logs collected this cycle: 62
2025-05-30 01:36:43 - root - DEBUG - Memory usage for LoggingAgent: 86.61 MB
2025-05-30 01:36:48 - root - DEBUG - Queued 62 logs for API transmission
2025-05-30 01:36:48 - root - INFO - Stopping Logging Agent...
2025-05-30 01:36:53 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:36:53 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:36:53 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:36:53 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:36:53 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:36:53 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:36:53 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:36:53 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:36:53 - utils.api_client - WARNING - Sent 0/130 logs to API
2025-05-30 01:36:54 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:36:54 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:36:54 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 01:37:54 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:37:54 - utils.api_client - INFO - Async API client started
2025-05-30 01:37:54 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:37:54 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:37:54 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:37:54 - utils.api_client - INFO - API connection test successful
2025-05-30 01:37:54 - root - INFO - API connection test successful (response time: 0.03s)
2025-05-30 01:37:54 - root - INFO - Event Log Collector initialized
2025-05-30 01:37:54 - root - INFO - Application Log Collector initialized
2025-05-30 01:37:54 - root - INFO - System Log Collector initialized
2025-05-30 01:37:54 - root - INFO - Network Log Collector initialized
2025-05-30 01:37:54 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:37:54 - root - INFO - Packet Collector initialized
2025-05-30 01:37:54 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:37:54 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:37:54 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:37:54 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:37:54 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:37:54 - utils.api_client - INFO - API connection test successful
2025-05-30 01:37:55 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:37:55 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:37:55 - utils.api_client - INFO - Async API client started
2025-05-30 01:37:55 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:37:55 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:37:55 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:37:55 - utils.api_client - INFO - API connection test successful
2025-05-30 01:37:55 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:37:55 - root - INFO - Event Log Collector initialized
2025-05-30 01:37:55 - root - INFO - Application Log Collector initialized
2025-05-30 01:37:55 - root - INFO - System Log Collector initialized
2025-05-30 01:37:55 - root - INFO - Network Log Collector initialized
2025-05-30 01:37:55 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:37:55 - root - INFO - Packet Collector initialized
2025-05-30 01:37:55 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:37:55 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:37:55 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:37:55 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:37:55 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 182
2025-05-30 01:37:55 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:37:55 - utils.api_client - DEBUG - Successfully sent log 1/2
2025-05-30 01:37:55 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 182
2025-05-30 01:37:55 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:37:55 - utils.api_client - DEBUG - Successfully sent log 2/2
2025-05-30 01:37:55 - utils.api_client - DEBUG - Successfully sent 2 logs to API
2025-05-30 01:37:55 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:37:55 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:37:55 - utils.api_client - INFO - Async API client started
2025-05-30 01:37:55 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:37:55 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:37:55 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:37:55 - utils.api_client - INFO - API connection test successful
2025-05-30 01:37:55 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:37:55 - root - INFO - Event Log Collector initialized
2025-05-30 01:37:55 - root - INFO - Application Log Collector initialized
2025-05-30 01:37:55 - root - INFO - System Log Collector initialized
2025-05-30 01:37:55 - root - INFO - Network Log Collector initialized
2025-05-30 01:37:55 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:37:55 - root - INFO - Packet Collector initialized
2025-05-30 01:37:55 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:37:55 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:37:55 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:37:55 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:37:55 - root - INFO - Logging Agent started successfully
2025-05-30 01:37:55 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:37:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 12 logs from System
2025-05-30 01:37:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:37:55 - root - DEBUG - Collected 31 logs from event_logs
2025-05-30 01:37:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:37:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:37:55 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:37:55 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:37:55 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:37:55 - root - DEBUG - Total logs collected this cycle: 57
2025-05-30 01:37:55 - root - DEBUG - Memory usage for LoggingAgent: 85.27 MB
2025-05-30 01:38:00 - root - DEBUG - Queued 57 logs for API transmission
2025-05-30 01:38:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 12 logs from System
2025-05-30 01:38:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:38:00 - root - DEBUG - Collected 31 logs from event_logs
2025-05-30 01:38:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:38:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:38:00 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:38:00 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:38:00 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:38:00 - root - DEBUG - Collected 13 logs from network_logs
2025-05-30 01:38:00 - root - DEBUG - Total logs collected this cycle: 70
2025-05-30 01:38:00 - root - DEBUG - Memory usage for LoggingAgent: 85.43 MB
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 1/57
2025-05-30 01:38:05 - root - DEBUG - Queued 70 logs for API transmission
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 2/57
2025-05-30 01:38:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 12 logs from System
2025-05-30 01:38:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:38:05 - root - DEBUG - Collected 31 logs from event_logs
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 3/57
2025-05-30 01:38:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:38:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:38:05 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:38:05 - logging_agent.collectors.event_log_collector - DEBUG - Collected 1 logs from System
2025-05-30 01:38:05 - root - DEBUG - Collected 1 logs from system_logs
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - root - DEBUG - Collected 11 logs from network_logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - root - DEBUG - Total logs collected this cycle: 68
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 4/57
2025-05-30 01:38:05 - root - DEBUG - Memory usage for LoggingAgent: 85.55 MB
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 5/57
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 6/57
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 7/57
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 8/57
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 9/57
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 10/57
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 11/57
2025-05-30 01:38:05 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:05 - utils.api_client - DEBUG - Successfully sent log 12/57
2025-05-30 01:38:06 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:38:06 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:38:06 - utils.api_client - DEBUG - Successfully sent log 13/57
2025-05-30 01:38:06 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 429 55
2025-05-30 01:38:06 - urllib3.util.retry - DEBUG - Incremented Retry for (url='/api/v1/logs'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-05-30 01:38:10 - root - DEBUG - Queued 68 logs for API transmission
2025-05-30 01:38:10 - root - INFO - Stopping Logging Agent...
2025-05-30 01:38:15 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:38:26 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 429 55
2025-05-30 01:38:26 - urllib3.util.retry - DEBUG - Incremented Retry for (url='/api/v1/logs'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - Retry: /api/v1/logs
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 14/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 15/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 16/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - Retry: /api/v1/logs
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (2): localhost:5000
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 17/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 1/138
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 18/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 2/138
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 19/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 3/138
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 20/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 4/138
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 21/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 5/138
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 22/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 6/138
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 23/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 7/138
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 24/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 8/138
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 25/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 9/138
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 26/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 27/57
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 10/138
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent log 28/57
2025-05-30 01:40:49 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:49 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 11/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 29/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 12/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 30/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 13/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 31/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 14/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 32/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 15/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 33/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 16/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 34/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 17/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 35/57
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 18/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 36/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 19/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 37/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 20/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 38/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 21/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 39/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 22/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 40/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 23/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 41/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 24/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 42/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 25/138
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 43/57
2025-05-30 01:40:50 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:50 - utils.api_client - DEBUG - Successfully sent log 26/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 44/57
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 27/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 45/57
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 28/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 46/57
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 47/57
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 29/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 48/57
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 30/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 49/57
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 31/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 50/57
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 32/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:51 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:51 - utils.api_client - WARNING - Failed to send log 51/57: 00863f25-c355-4b03-ae16-30dcbac17be4
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:51 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:51 - utils.api_client - WARNING - Failed to send log 52/57: 1b6abf7d-418e-4905-9478-62b203f5f78f
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 33/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:51 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:51 - utils.api_client - WARNING - Failed to send log 53/57: 1b9fb546-e448-425d-9bc0-808dd7343fb5
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 34/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:51 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:51 - utils.api_client - WARNING - Failed to send log 54/57: f933e7b9-5aeb-4b2f-9bc4-8ea03d6d3526
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:51 - utils.api_client - DEBUG - Successfully sent log 35/138
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:51 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:51 - utils.api_client - WARNING - Failed to send log 55/57: 6af8fa02-af01-45c0-9413-df3d1d6a784f
2025-05-30 01:40:51 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 36/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:52 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:52 - utils.api_client - WARNING - Failed to send log 56/57: 7fbbc93e-e7c2-4372-9f79-d78c2498d070
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 37/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:52 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:52 - utils.api_client - WARNING - Failed to send log 57/57: a811f1c7-b308-4304-86f7-1476a7500a09
2025-05-30 01:40:52 - utils.api_client - WARNING - Failed to send 7 out of 57 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Failed log indices: [50, 51, 52, 53, 54, 55, 56]
2025-05-30 01:40:52 - utils.api_client - WARNING - Sent 50/57 logs to API
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 38/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 39/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 40/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 41/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 42/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 43/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 44/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 45/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 46/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 47/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 48/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 49/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 201 203
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent batch of 1 logs
2025-05-30 01:40:52 - utils.api_client - DEBUG - Successfully sent log 50/138
2025-05-30 01:40:52 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:52 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:52 - utils.api_client - WARNING - Failed to send log 51/138: cb577a3e-c2ae-4a83-bdb7-9c7826901644
2025-05-30 01:40:53 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:53 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:53 - utils.api_client - WARNING - Failed to send log 52/138: 6550e415-8558-4dd5-8391-637548266038
2025-05-30 01:40:53 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:53 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:53 - utils.api_client - WARNING - Failed to send log 53/138: 8288f60b-a9d1-4254-92b8-017128b5675f
2025-05-30 01:40:53 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:53 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:53 - utils.api_client - WARNING - Failed to send log 54/138: 3be6d982-04f9-4cae-8a0a-ca0c634547ce
2025-05-30 01:40:53 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:53 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:53 - utils.api_client - WARNING - Failed to send log 55/138: 3d178468-0ed7-4f18-bed2-124eefdc68e9
2025-05-30 01:40:53 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:40:53 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:40:53 - utils.api_client - WARNING - Failed to send log 56/138: d0b12557-6ae0-4c0b-b705-36226656b091
2025-05-30 01:40:53 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 429 55
2025-05-30 01:40:53 - urllib3.util.retry - DEBUG - Incremented Retry for (url='/api/v1/logs'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-05-30 01:42:40 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:42:40 - root - INFO - Agent is not running
2025-05-30 01:42:53 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:42:53 - root - INFO - Agent is not running
2025-05-30 01:42:53 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:42:53 - root - INFO - Agent is not running
2025-05-30 01:43:14 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:14 - root - INFO - Agent is not running
2025-05-30 01:43:20 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:20 - root - INFO - Agent is not running
2025-05-30 01:43:20 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:20 - root - INFO - Agent is not running
2025-05-30 01:43:24 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:24 - root - INFO - Agent is not running
2025-05-30 01:43:25 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:25 - root - INFO - Agent is not running
2025-05-30 01:43:25 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:25 - root - INFO - Agent is not running
2025-05-30 01:43:25 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:25 - root - INFO - Agent is not running
2025-05-30 01:43:26 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:26 - root - INFO - Agent is not running
2025-05-30 01:43:26 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:26 - root - INFO - Agent is not running
2025-05-30 01:43:26 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:26 - root - INFO - Agent is not running
2025-05-30 01:43:26 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:26 - root - INFO - Agent is not running
2025-05-30 01:43:26 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:43:26 - root - INFO - Agent is not running
2025-05-30 01:44:02 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:44:02 - root - INFO - Agent is not running
2025-05-30 01:44:12 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:44:12 - root - INFO - Agent is not running
2025-05-30 01:44:12 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:44:12 - root - INFO - Agent is not running
2025-05-30 01:44:56 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:44:56 - root - INFO - Agent is not running
2025-05-30 01:45:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:45:03 - root - INFO - Agent is not running
2025-05-30 01:45:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:45:03 - root - INFO - Agent is not running
2025-05-30 01:46:02 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:02 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - utils.api_client - ERROR - Unexpected error sending batch: reentrant call inside <_io.BufferedWriter name='<stderr>'>
2025-05-30 01:46:03 - utils.api_client - WARNING - Failed to send log 57/138: e665cc69-8cb8-470e-8261-148b10302504
2025-05-30 01:46:03 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 429 55
2025-05-30 01:46:03 - urllib3.util.retry - DEBUG - Incremented Retry for (url='/api/v1/logs'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:03 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:03 - root - INFO - Agent is not running
2025-05-30 01:46:08 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:08 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:09 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:09 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - utils.api_client - ERROR - Unexpected error sending batch: reentrant call inside <_io.BufferedWriter name='<stderr>'>
2025-05-30 01:46:10 - utils.api_client - WARNING - Failed to send log 58/138: 63464a7f-68a6-4423-a4e1-4dd7e6b864f2
2025-05-30 01:46:10 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 429 55
2025-05-30 01:46:10 - urllib3.util.retry - DEBUG - Incremented Retry for (url='/api/v1/logs'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:10 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:10 - root - INFO - Agent is not running
2025-05-30 01:46:24 - root - INFO - Received signal 2, shutting down...
2025-05-30 01:46:24 - root - INFO - Agent is not running
2025-05-30 01:50:39 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:50:39 - utils.api_client - INFO - Async API client started
2025-05-30 01:50:39 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:50:39 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:50:39 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 429 55
2025-05-30 01:50:39 - urllib3.util.retry - DEBUG - Incremented Retry for (url='/api/v1/logs'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-05-30 01:54:28 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:54:28 - utils.api_client - INFO - Async API client started
2025-05-30 01:54:28 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:54:28 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:54:28 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 429 55
2025-05-30 01:54:28 - urllib3.util.retry - DEBUG - Incremented Retry for (url='/api/v1/logs'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-05-30 01:56:14 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-05-30 01:56:14 - utils.api_client - INFO - Async API client started
2025-05-30 01:56:14 - root - INFO - Async API client initialized for dashboard integration
2025-05-30 01:56:14 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:5000
2025-05-30 01:56:14 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:56:14 - utils.api_client - INFO - API connection test successful
2025-05-30 01:56:14 - root - INFO - API connection test successful (response time: 0.02s)
2025-05-30 01:56:14 - root - INFO - Event Log Collector initialized
2025-05-30 01:56:14 - root - INFO - Application Log Collector initialized
2025-05-30 01:56:14 - root - INFO - System Log Collector initialized
2025-05-30 01:56:14 - root - INFO - Network Log Collector initialized
2025-05-30 01:56:14 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:56:14 - root - INFO - Packet Collector initialized
2025-05-30 01:56:14 - root - DEBUG - Signal handlers set up successfully
2025-05-30 01:56:14 - root - INFO - Logging Agent initialized successfully
2025-05-30 01:56:14 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-05-30 01:56:14 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-05-30 01:56:14 - root - INFO - Logging Agent started successfully
2025-05-30 01:56:14 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 01:56:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:14 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:14 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:14 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:14 - root - DEBUG - Total logs collected this cycle: 55
2025-05-30 01:56:14 - root - DEBUG - Memory usage for LoggingAgent: 84.55 MB
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:19 - root - DEBUG - Queued 55 logs for API transmission
2025-05-30 01:56:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:19 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:19 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:19 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:19 - root - DEBUG - Collected 10 logs from network_logs
2025-05-30 01:56:19 - root - DEBUG - Total logs collected this cycle: 65
2025-05-30 01:56:19 - root - DEBUG - Memory usage for LoggingAgent: 84.88 MB
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:24 - root - DEBUG - Queued 65 logs for API transmission
2025-05-30 01:56:24 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:56:24 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:56:24 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:56:24 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:56:24 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:56:24 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:56:24 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:56:24 - utils.api_client - WARNING - Sent 0/120 logs to API
2025-05-30 01:56:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:24 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:24 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:24 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:24 - root - DEBUG - Collected 15 logs from network_logs
2025-05-30 01:56:24 - root - DEBUG - Total logs collected this cycle: 70
2025-05-30 01:56:24 - root - DEBUG - Memory usage for LoggingAgent: 85.83 MB
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:29 - root - DEBUG - Queued 70 logs for API transmission
2025-05-30 01:56:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:29 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:29 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:29 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:29 - root - DEBUG - Collected 16 logs from network_logs
2025-05-30 01:56:29 - root - DEBUG - Total logs collected this cycle: 71
2025-05-30 01:56:29 - root - DEBUG - Memory usage for LoggingAgent: 85.02 MB
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:34 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:56:34 - root - DEBUG - Queued 71 logs for API transmission
2025-05-30 01:56:34 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:56:34 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:56:34 - utils.api_client - WARNING - Sent 0/70 logs to API
2025-05-30 01:56:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:34 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:34 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:34 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:34 - root - DEBUG - Collected 10 logs from network_logs
2025-05-30 01:56:34 - root - DEBUG - Total logs collected this cycle: 65
2025-05-30 01:56:34 - root - DEBUG - Memory usage for LoggingAgent: 85.16 MB
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:39 - root - DEBUG - Queued 65 logs for API transmission
2025-05-30 01:56:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:39 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:39 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:39 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:39 - root - DEBUG - Collected 11 logs from network_logs
2025-05-30 01:56:39 - root - DEBUG - Total logs collected this cycle: 66
2025-05-30 01:56:39 - root - DEBUG - Memory usage for LoggingAgent: 85.97 MB
2025-05-30 01:56:44 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:56:44 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:56:44 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:56:44 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:44 - root - DEBUG - Queued 66 logs for API transmission
2025-05-30 01:56:44 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:44 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:56:44 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:44 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:56:44 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:56:44 - utils.api_client - WARNING - Sent 0/136 logs to API
2025-05-30 01:56:44 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:44 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:44 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:44 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:44 - root - DEBUG - Collected 8 logs from network_logs
2025-05-30 01:56:44 - root - DEBUG - Total logs collected this cycle: 63
2025-05-30 01:56:44 - root - DEBUG - Memory usage for LoggingAgent: 86.27 MB
2025-05-30 01:56:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:49 - root - DEBUG - Queued 63 logs for API transmission
2025-05-30 01:56:49 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:49 - root - DEBUG - Collected 5 logs from network_logs
2025-05-30 01:56:49 - root - DEBUG - Total logs collected this cycle: 60
2025-05-30 01:56:49 - root - DEBUG - Memory usage for LoggingAgent: 86.29 MB
2025-05-30 01:56:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:54 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:54 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:56:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:54 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:54 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:54 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - root - DEBUG - Collected 10 logs from network_logs
2025-05-30 01:56:54 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - root - DEBUG - Total logs collected this cycle: 65
2025-05-30 01:56:54 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - root - DEBUG - Memory usage for LoggingAgent: 86.30 MB
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - utils.api_client - WARNING - Sent 0/129 logs to API
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:54 - root - DEBUG - Queued 115 logs for API transmission
2025-05-30 01:56:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:56:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:59 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:56:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:56:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:56:59 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:56:59 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:56:59 - root - DEBUG - Collected 7 logs from network_logs
2025-05-30 01:56:59 - root - DEBUG - Total logs collected this cycle: 62
2025-05-30 01:56:59 - root - DEBUG - Memory usage for LoggingAgent: 86.33 MB
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:56:59 - root - DEBUG - Queued 72 logs for API transmission
2025-05-30 01:57:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for System
2025-05-30 01:57:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for Application
2025-05-30 01:57:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:57:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for Application
2025-05-30 01:57:00 - logging_agent.collectors.event_log_collector - DEBUG - Access test successful for System
2025-05-30 01:57:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:57:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:57:04 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:57:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:57:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:57:04 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:57:04 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:57:04 - root - DEBUG - Collected 9 logs from network_logs
2025-05-30 01:57:04 - root - DEBUG - Total logs collected this cycle: 64
2025-05-30 01:57:04 - root - DEBUG - Memory usage for LoggingAgent: 86.34 MB
2025-05-30 01:57:04 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:57:04 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:57:04 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:57:04 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:57:04 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:57:04 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:57:04 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:57:04 - utils.api_client - WARNING - Sent 0/187 logs to API
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:04 - root - DEBUG - Queued 64 logs for API transmission
2025-05-30 01:57:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 11 logs from System
2025-05-30 01:57:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:57:09 - root - DEBUG - Collected 30 logs from event_logs
2025-05-30 01:57:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 6 logs from Microsoft-Windows-Diagnostics-Performance/Operational
2025-05-30 01:57:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 19 logs from Application
2025-05-30 01:57:09 - root - DEBUG - Collected 25 logs from application_logs
2025-05-30 01:57:09 - logging_agent.collectors.event_log_collector - DEBUG - Collected 0 logs from System
2025-05-30 01:57:09 - root - DEBUG - Collected 10 logs from network_logs
2025-05-30 01:57:09 - root - DEBUG - Total logs collected this cycle: 65
2025-05-30 01:57:09 - root - DEBUG - Memory usage for LoggingAgent: 86.46 MB
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - log_standardizer.standardizer - DEBUG - Added missing field 'raw_data' with default value
2025-05-30 01:57:09 - root - DEBUG - Queued 65 logs for API transmission
2025-05-30 01:57:12 - root - INFO - Stopping Logging Agent...
2025-05-30 01:57:14 - urllib3.connectionpool - DEBUG - Resetting dropped connection: localhost
2025-05-30 01:57:14 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:57:14 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:57:14 - utils.api_client - ERROR - Failed to send batch 1
2025-05-30 01:57:14 - urllib3.connectionpool - DEBUG - http://localhost:5000 "POST /api/v1/logs HTTP/11" 400 643
2025-05-30 01:57:14 - utils.api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-05-30 01:57:14 - utils.api_client - ERROR - Failed to send batch 2
2025-05-30 01:57:14 - utils.api_client - WARNING - Sent 0/129 logs to API
2025-05-30 01:57:17 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 01:57:17 - utils.api_client - INFO - Async API client stopped
2025-05-30 01:57:17 - root - INFO - Logging Agent stopped successfully
2025-05-30 01:57:17 - audit_logger - INFO - Python Logging Agent service stopped
2025-05-30 17:07:46 - root - INFO - Event Log Collector initialized
2025-05-30 17:07:46 - root - INFO - Application Log Collector initialized
2025-05-30 17:07:46 - root - INFO - System Log Collector initialized
2025-05-30 17:07:47 - root - INFO - Network Log Collector initialized
2025-05-30 17:07:47 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{D15C6CD7-3606-43AF-B14A-A860991A0EAA}
2025-05-30 17:07:47 - root - INFO - Packet Collector initialized
2025-05-30 17:07:47 - root - INFO - Logging Agent initialized successfully
2025-05-30 17:07:47 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{D15C6CD7-3606-43AF-B14A-A860991A0EAA} with filter: ''
2025-05-30 17:07:47 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{D15C6CD7-3606-43AF-B14A-A860991A0EAA}
2025-05-30 17:07:47 - root - INFO - Logging Agent started successfully
2025-05-30 17:07:47 - audit_logger - INFO - Python Logging Agent service started
2025-05-30 17:08:15 - root - INFO - Stopping Logging Agent...
2025-05-30 17:08:20 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-05-30 17:08:21 - root - INFO - Logging Agent stopped successfully
2025-05-30 17:08:21 - audit_logger - INFO - Python Logging Agent service stopped
2025-06-02 09:22:43 - root - INFO - Event Log Collector initialized
2025-06-02 09:22:43 - root - INFO - Application Log Collector initialized
2025-06-02 09:22:43 - root - INFO - System Log Collector initialized
2025-06-02 09:22:43 - root - INFO - Network Log Collector initialized
2025-06-02 09:22:43 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-06-02 09:22:43 - root - INFO - Packet Collector initialized
2025-06-02 09:22:43 - root - INFO - Logging Agent initialized successfully
2025-06-02 09:22:43 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7} with filter: ''
2025-06-02 09:22:43 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{9DF09C76-EAC4-405A-854C-F6BA0AF916C7}
2025-06-02 09:22:43 - root - INFO - Logging Agent started successfully
2025-06-02 09:22:43 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 09:22:45 - root - INFO - Stopping Logging Agent...
2025-06-02 09:22:50 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-06-02 09:22:50 - root - INFO - Logging Agent stopped successfully
2025-06-02 09:22:50 - audit_logger - INFO - Python Logging Agent service stopped
2025-06-02 10:08:05 - root - INFO - Event Log Collector initialized
2025-06-02 10:08:05 - root - INFO - Application Log Collector initialized
2025-06-02 10:08:05 - root - INFO - System Log Collector initialized
2025-06-02 10:08:05 - root - INFO - Network Log Collector initialized
2025-06-02 10:08:05 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:08:05 - root - INFO - Packet Collector initialized
2025-06-02 10:08:05 - root - INFO - ExLog API client disabled in configuration
2025-06-02 10:08:05 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:08:05 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:08:05 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:08:05 - root - INFO - Logging Agent started successfully
2025-06-02 10:08:05 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:10:18 - root - INFO - Stopping Logging Agent...
2025-06-02 10:10:19 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-06-02 10:10:19 - root - INFO - Logging Agent stopped successfully
2025-06-02 10:10:19 - audit_logger - INFO - Python Logging Agent service stopped
2025-06-02 10:13:46 - root - INFO - Event Log Collector initialized
2025-06-02 10:13:46 - root - INFO - Application Log Collector initialized
2025-06-02 10:13:46 - root - INFO - System Log Collector initialized
2025-06-02 10:13:46 - root - INFO - Network Log Collector initialized
2025-06-02 10:13:46 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:13:46 - root - INFO - Packet Collector initialized
2025-06-02 10:13:46 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-06-02 10:13:46 - utils.api_client - INFO - Async API client started
2025-06-02 10:13:46 - utils.api_client - INFO - API connection test successful
2025-06-02 10:13:46 - root - INFO - API connection test successful (response time: 0.03s)
2025-06-02 10:13:46 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 10:13:46 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:13:46 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:13:46 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:13:46 - root - INFO - Logging Agent started successfully
2025-06-02 10:13:46 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:13:56 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:13:56 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:14:00 - logging_agent.collectors.event_log_collector - WARNING - Access test failed for Application: (6, 'CloseEventLog', 'The handle is invalid.')
2025-06-02 10:14:01 - utils.api_client - ERROR - Error sending batch (attempt 2): Event loop is closed
2025-06-02 10:14:01 - utils.api_client - INFO - Retrying in 10 seconds
2025-06-02 10:14:07 - root - INFO - Stopping Logging Agent...
2025-06-02 10:14:11 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-06-02 10:14:11 - utils.api_client - ERROR - Error sending batch (attempt 3): Event loop is closed
2025-06-02 10:14:11 - utils.api_client - INFO - Retrying in 20 seconds
2025-06-02 10:14:11 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:14:11 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:14:16 - utils.api_client - ERROR - Error sending batch (attempt 2): Event loop is closed
2025-06-02 10:14:16 - utils.api_client - INFO - Retrying in 10 seconds
2025-06-02 10:14:26 - utils.api_client - ERROR - Error sending batch (attempt 3): Event loop is closed
2025-06-02 10:14:26 - utils.api_client - INFO - Retrying in 20 seconds
2025-06-02 10:14:28 - root - INFO - Agent is not running
2025-06-02 10:18:40 - root - INFO - Event Log Collector initialized
2025-06-02 10:18:40 - root - INFO - Application Log Collector initialized
2025-06-02 10:18:40 - root - INFO - System Log Collector initialized
2025-06-02 10:18:41 - root - INFO - Network Log Collector initialized
2025-06-02 10:18:41 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:18:41 - root - INFO - Packet Collector initialized
2025-06-02 10:18:41 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-06-02 10:18:41 - utils.api_client - INFO - Async API client started
2025-06-02 10:18:41 - utils.api_client - INFO - API connection test successful
2025-06-02 10:18:41 - root - INFO - API connection test successful (response time: 0.02s)
2025-06-02 10:18:41 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 10:18:41 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:18:41 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:18:41 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:18:41 - root - INFO - Logging Agent started successfully
2025-06-02 10:18:41 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:18:48 - root - INFO - Stopping Logging Agent...
2025-06-02 10:18:50 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:18:50 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:18:53 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-06-02 10:18:53 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:18:53 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:18:55 - utils.api_client - ERROR - Error sending batch (attempt 2): Event loop is closed
2025-06-02 10:18:55 - utils.api_client - INFO - Retrying in 10 seconds
2025-06-02 10:18:56 - root - INFO - Agent is not running
2025-06-02 10:20:46 - root - INFO - Event Log Collector initialized
2025-06-02 10:20:46 - root - INFO - Application Log Collector initialized
2025-06-02 10:20:46 - root - INFO - System Log Collector initialized
2025-06-02 10:20:46 - root - INFO - Network Log Collector initialized
2025-06-02 10:20:46 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:20:46 - root - INFO - Packet Collector initialized
2025-06-02 10:20:46 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-06-02 10:20:46 - utils.api_client - INFO - Async API client started
2025-06-02 10:20:46 - utils.api_client - INFO - API connection test successful
2025-06-02 10:20:46 - root - INFO - API connection test successful (response time: 0.02s)
2025-06-02 10:20:46 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 10:20:46 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:20:46 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:20:46 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:20:46 - root - INFO - Logging Agent started successfully
2025-06-02 10:20:46 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:20:56 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:20:56 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:21:01 - utils.api_client - ERROR - Error sending batch (attempt 2): Event loop is closed
2025-06-02 10:21:01 - utils.api_client - INFO - Retrying in 10 seconds
2025-06-02 10:21:03 - root - INFO - Stopping Logging Agent...
2025-06-02 10:21:03 - root - INFO - Agent is not running
2025-06-02 10:25:48 - root - INFO - Event Log Collector initialized
2025-06-02 10:25:48 - root - INFO - Application Log Collector initialized
2025-06-02 10:25:48 - root - INFO - System Log Collector initialized
2025-06-02 10:25:48 - root - INFO - Network Log Collector initialized
2025-06-02 10:25:48 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:25:48 - root - INFO - Packet Collector initialized
2025-06-02 10:25:48 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-06-02 10:25:48 - utils.api_client - INFO - Async API client started
2025-06-02 10:25:48 - utils.api_client - INFO - API connection test successful
2025-06-02 10:25:49 - root - INFO - API connection test successful (response time: 0.02s)
2025-06-02 10:25:49 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 10:25:49 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:25:49 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:25:49 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:25:49 - root - INFO - Logging Agent started successfully
2025-06-02 10:25:49 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:25:58 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:25:58 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:26:00 - logging_agent.collectors.event_log_collector - WARNING - Access test failed for Application: (6, 'CloseEventLog', 'The handle is invalid.')
2025-06-02 10:26:03 - utils.api_client - ERROR - Error sending batch (attempt 2): Event loop is closed
2025-06-02 10:26:03 - utils.api_client - INFO - Retrying in 10 seconds
2025-06-02 10:26:13 - utils.api_client - ERROR - Error sending batch (attempt 3): Event loop is closed
2025-06-02 10:26:13 - utils.api_client - INFO - Retrying in 20 seconds
2025-06-02 10:26:16 - root - INFO - Stopping Logging Agent...
2025-06-02 10:26:21 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-06-02 10:26:21 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:26:21 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:26:26 - utils.api_client - ERROR - Error sending batch (attempt 2): Event loop is closed
2025-06-02 10:26:26 - root - INFO - Logging Agent stopped successfully
2025-06-02 10:26:26 - utils.api_client - INFO - Retrying in 10 seconds
2025-06-02 10:26:26 - audit_logger - INFO - Python Logging Agent service stopped
2025-06-02 10:35:04 - root - INFO - Event Log Collector initialized
2025-06-02 10:35:04 - root - INFO - Application Log Collector initialized
2025-06-02 10:35:04 - root - INFO - System Log Collector initialized
2025-06-02 10:35:04 - root - INFO - Network Log Collector initialized
2025-06-02 10:35:04 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:35:04 - root - INFO - Packet Collector initialized
2025-06-02 10:35:04 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-06-02 10:35:04 - utils.api_client - INFO - Async API client started
2025-06-02 10:35:04 - utils.api_client - INFO - API connection test successful
2025-06-02 10:35:05 - root - INFO - API connection test successful (response time: 0.01s)
2025-06-02 10:35:05 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 10:35:05 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:35:05 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:35:05 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:35:05 - root - INFO - Logging Agent started successfully
2025-06-02 10:35:05 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:35:09 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:35:09 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:35:14 - root - INFO - Stopping Logging Agent...
2025-06-02 10:35:14 - utils.api_client - ERROR - Error sending batch (attempt 2): Event loop is closed
2025-06-02 10:35:14 - utils.api_client - INFO - Retrying in 10 seconds
2025-06-02 10:35:19 - root - INFO - Agent is not running
2025-06-02 10:38:19 - root - INFO - Event Log Collector initialized
2025-06-02 10:38:19 - root - INFO - Application Log Collector initialized
2025-06-02 10:38:19 - root - INFO - System Log Collector initialized
2025-06-02 10:38:20 - root - INFO - Network Log Collector initialized
2025-06-02 10:38:20 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:38:20 - root - INFO - Packet Collector initialized
2025-06-02 10:38:20 - utils.api_client - INFO - API endpoint configured: http://localhost:5000/api/v1/logs
2025-06-02 10:38:20 - utils.api_client - INFO - Async API client started
2025-06-02 10:38:20 - utils.api_client - INFO - API connection test successful
2025-06-02 10:38:21 - root - INFO - API connection test successful (response time: 0.01s)
2025-06-02 10:38:21 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 10:38:21 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:38:21 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:38:21 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:38:21 - root - INFO - Logging Agent started successfully
2025-06-02 10:38:21 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:38:24 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:38:24 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:38:25 - root - INFO - Stopping Logging Agent...
2025-06-02 10:38:29 - utils.api_client - ERROR - Error sending batch (attempt 2): Event loop is closed
2025-06-02 10:38:29 - utils.api_client - INFO - Retrying in 10 seconds
2025-06-02 10:38:30 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-06-02 10:38:30 - utils.api_client - ERROR - Error sending batch (attempt 1): Event loop is closed
2025-06-02 10:38:30 - utils.api_client - INFO - Retrying in 5 seconds
2025-06-02 10:38:35 - utils.api_client - ERROR - Error sending batch (attempt 2): Event loop is closed
2025-06-02 10:38:35 - root - INFO - Agent is not running
2025-06-02 10:38:35 - utils.api_client - INFO - Retrying in 10 seconds
2025-06-02 10:45:46 - root - INFO - Event Log Collector initialized
2025-06-02 10:45:46 - root - INFO - Application Log Collector initialized
2025-06-02 10:45:46 - root - INFO - System Log Collector initialized
2025-06-02 10:45:47 - root - INFO - Network Log Collector initialized
2025-06-02 10:45:47 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:45:47 - root - INFO - Packet Collector initialized
2025-06-02 10:45:47 - utils.simple_api_client - INFO - Simple API client configured: http://localhost:5000/api/v1/logs
2025-06-02 10:45:47 - utils.simple_api_client - INFO - Simple API client started
2025-06-02 10:45:47 - utils.simple_api_client - INFO - API connection test successful
2025-06-02 10:45:47 - root - INFO - API connection test successful (response time: 0.02s)
2025-06-02 10:45:47 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 10:45:47 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:45:47 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:45:47 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:45:47 - root - INFO - Logging Agent started successfully
2025-06-02 10:45:47 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:45:52 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:45:52 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:45:52 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:45:52 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:45:52 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:45:52 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:45:52 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:45:52 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:45:52 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:45:57 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:45:57 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:45:57 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:45:57 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:45:57 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:45:57 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:45:57 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:45:57 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:45:57 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:45:57 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:46:02 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:46:02 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:46:02 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:46:02 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:46:02 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:46:02 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:46:02 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:46:02 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:46:02 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:46:02 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:46:04 - root - INFO - Stopping Logging Agent...
2025-06-02 10:46:07 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:46:07 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:46:07 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:46:07 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:46:07 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:46:07 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:46:07 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:46:07 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:46:07 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:46:07 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:46:07 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:46:09 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-06-02 10:46:10 - utils.simple_api_client - INFO - Simple API client stopped
2025-06-02 10:46:10 - root - INFO - Logging Agent stopped successfully
2025-06-02 10:46:10 - audit_logger - INFO - Python Logging Agent service stopped
2025-06-02 10:54:20 - root - INFO - Event Log Collector initialized
2025-06-02 10:54:20 - root - INFO - Application Log Collector initialized
2025-06-02 10:54:20 - root - INFO - System Log Collector initialized
2025-06-02 10:54:21 - root - INFO - Network Log Collector initialized
2025-06-02 10:54:21 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:54:21 - root - INFO - Packet Collector initialized
2025-06-02 10:54:21 - utils.simple_api_client - INFO - Simple API client configured: http://localhost:5000/api/v1/logs
2025-06-02 10:54:21 - utils.simple_api_client - INFO - Simple API client started
2025-06-02 10:54:21 - utils.simple_api_client - INFO - API connection test successful
2025-06-02 10:54:21 - root - INFO - API connection test successful (response time: 0.02s)
2025-06-02 10:54:21 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 10:54:21 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:54:21 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:54:21 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:54:21 - root - INFO - Logging Agent started successfully
2025-06-02 10:54:21 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:54:25 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:54:26 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:54:26 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:54:26 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:54:26 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:54:26 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:54:26 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:54:26 - utils.simple_api_client - ERROR - Problematic logs that caused validation error:
2025-06-02 10:54:26 - utils.simple_api_client - ERROR - Log 1: {
  "log_id": "f86e4659-0462-45b4-b450-6352a8f3f90c",
  "timestamp": "2025-06-02T09:52:17Z",
  "source": "Application",
  "source_type": "event",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "<The description for Event ID ( 0 ) in Source ( 'vmware-converter-server' ) could not be found. It contains the following insertion string(s):'vmware-converter-server: service started'.>",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39420,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "vmware-converter-server: service started"
    ],
    "data": null,
    "event_id": 0,
    "event_category": 0,
    "event_type": 4,
    "metadata": {
      "collection_time": "2025-06-02T10:54:25.951620",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "windows_event_log": true,
      "event_log_source": "Application"
    }
  }
}
2025-06-02 10:54:26 - utils.simple_api_client - ERROR - Log 2: {
  "log_id": "2a772bcf-747f-47c3-80f7-6cddd28719c3",
  "timestamp": "2025-06-02T09:51:41Z",
  "source": "Microsoft-Windows-Diagnostics-Performance/Operational",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "warning",
  "message": "<The description for Event ID ( 101 ) in Source ( 'Microsoft-Windows-Diagnostics-Performance' ) could not be found. It contains the following insertion string(s):'2025-06-02T13:49:37.8896136Z, 15, powershell.exe, 19, Windows PowerShell, 36, 10.0.22621.1 (WinBuild.160101.0800), 1004, 4, 58, C:\\\\Windows\\\\System32\\\\WindowsPowerShell\\\\v1.0\\\\powershell.exe, 37, Microsoft\u00ae Windows\u00ae Operating System, 22, Microsoft Corporation'.>",
  "raw_data": null,
  "additional_fields": {
    "record_number": 207,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "2025-06-02T13:49:37.8896136Z",
      "15",
      "powershell.exe",
      "19",
      "Windows PowerShell",
      "36",
      "10.0.22621.1 (WinBuild.160101.0800)",
      "1004",
      "4",
      "58",
      "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe",
      "37",
      "Microsoft\u00ae Windows\u00ae Operating System",
      "22",
      "Microsoft Corporation"
    ],
    "data": null,
    "microsoft_source": "Microsoft-Windows-Diagnostics-Performance/Operational",
    "event_id": 101,
    "event_category": 4002,
    "event_type": 2,
    "event_description": "Application stopped",
    "metadata": {
      "collection_time": "2025-06-02T10:54:25.951620",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "application_lifecycle"
    }
  }
}
2025-06-02 10:54:26 - utils.simple_api_client - ERROR - Log 3: {
  "log_id": "dad30cba-cc9c-4f75-8387-ecd61dac0acf",
  "timestamp": "2025-06-02T09:51:41Z",
  "source": "Microsoft-Windows-Diagnostics-Performance/Operational",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "warning",
  "message": "<The description for Event ID ( 101 ) in Source ( 'Microsoft-Windows-Diagnostics-Performance' ) could not be found. It contains the following insertion string(s):'2025-06-02T13:49:37.8896136Z, 11, Sysmon.exe, 24, System activity monitor, 6, 14.13, 766, 388, 22, C:\\\\Windows\\\\Sysmon.exe, 20, Sysinternals Sysmon, 36, Sysinternals - www.sysinternals.com'.>",
  "raw_data": null,
  "additional_fields": {
    "record_number": 206,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "2025-06-02T13:49:37.8896136Z",
      "11",
      "Sysmon.exe",
      "24",
      "System activity monitor",
      "6",
      "14.13",
      "766",
      "388",
      "22",
      "C:\\Windows\\Sysmon.exe",
      "20",
      "Sysinternals Sysmon",
      "36",
      "Sysinternals - www.sysinternals.com"
    ],
    "data": null,
    "microsoft_source": "Microsoft-Windows-Diagnostics-Performance/Operational",
    "event_id": 101,
    "event_category": 4002,
    "event_type": 2,
    "event_description": "Application stopped",
    "metadata": {
      "collection_time": "2025-06-02T10:54:25.951620",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "application_lifecycle"
    }
  }
}
2025-06-02 10:54:26 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:54:26 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:54:28 - root - INFO - Stopping Logging Agent...
2025-06-02 10:54:30 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:54:30 - utils.simple_api_client - ERROR - Problematic logs that caused validation error:
2025-06-02 10:54:30 - utils.simple_api_client - ERROR - Log 1: {
  "log_id": "3396f43f-92ea-448d-8340-dcc5161c179c",
  "timestamp": "2025-06-02T09:52:20Z",
  "source": "Application",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "Service QsirchInitialized action for exit code 0 is Exit.\r\nExiting.",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39426,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "QsirchInitialized",
      "0",
      "Exit"
    ],
    "data": null,
    "event_id": 1016,
    "event_category": 0,
    "event_type": 4,
    "event_description": "Application event",
    "metadata": {
      "collection_time": "2025-06-02T10:54:25.952620",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "general_application"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 2: {
  "log_id": "b55ef36d-9341-4ec6-ae0d-acebc77b7338",
  "timestamp": "2025-06-02T09:52:20Z",
  "source": "Application",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "Killing PID 23756 in process tree of PID 23756 because service QsirchInitialized is stopping.",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39425,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "23756",
      "23756",
      "QsirchInitialized"
    ],
    "data": null,
    "event_id": 1027,
    "event_category": 0,
    "event_type": 4,
    "event_description": "Application event",
    "metadata": {
      "collection_time": "2025-06-02T10:54:25.952620",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "general_application"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 3: {
  "log_id": "a8f54365-89b1-4c72-8dd1-b8d9ce41aad6",
  "timestamp": "2025-06-02T09:52:20Z",
  "source": "Application",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "Killing process tree of process 23756 for service QsirchInitialized with exit code 0",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39424,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "QsirchInitialized",
      "23756",
      "0"
    ],
    "data": null,
    "event_id": 1023,
    "event_category": 0,
    "event_type": 4,
    "event_description": "Application event",
    "metadata": {
      "collection_time": "2025-06-02T10:54:25.952620",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "general_application"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:54:31 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:54:31 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:54:31 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Problematic logs that caused validation error:
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 1: {
  "log_id": "de2825b3-70f0-4a7d-b88f-ef31827dd9da",
  "timestamp": "2025-06-02T09:52:22Z",
  "source": "Application",
  "source_type": "event",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "Updated Windows Defender status successfully to SECURITY_PRODUCT_STATE_ON.",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39428,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "Windows Defender",
      "SECURITY_PRODUCT_STATE_ON"
    ],
    "data": null,
    "event_id": 15,
    "event_category": 0,
    "event_type": 4,
    "metadata": {
      "collection_time": "2025-06-02T10:54:30.959989",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "windows_event_log": true,
      "event_log_source": "Application"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 2: {
  "log_id": "491b13b7-9845-4d0d-86a7-d54d4958d516",
  "timestamp": "2025-06-02T09:52:20Z",
  "source": "Application",
  "source_type": "event",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "Requested stop of service QsirchInitialized.  No action is required as program C:\\Program Files (x86)\\QNAP\\Qsirch\\utils\\qsirch.bat is not running.",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39427,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "QsirchInitialized",
      "C:\\Program Files (x86)\\QNAP\\Qsirch\\utils\\qsirch.bat"
    ],
    "data": null,
    "event_id": 1012,
    "event_category": 0,
    "event_type": 4,
    "metadata": {
      "collection_time": "2025-06-02T10:54:30.959989",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "windows_event_log": true,
      "event_log_source": "Application"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 3: {
  "log_id": "e6354e9d-c211-4ee2-87a3-fa521e638641",
  "timestamp": "2025-06-02T09:52:20Z",
  "source": "Application",
  "source_type": "event",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "Service QsirchInitialized action for exit code 0 is Exit.\r\nExiting.",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39426,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "QsirchInitialized",
      "0",
      "Exit"
    ],
    "data": null,
    "event_id": 1016,
    "event_category": 0,
    "event_type": 4,
    "metadata": {
      "collection_time": "2025-06-02T10:54:30.959989",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "windows_event_log": true,
      "event_log_source": "Application"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Problematic logs that caused validation error:
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 1: {
  "log_id": "13be4410-8aad-41ae-ace8-4b19e60fa36b",
  "timestamp": "2025-06-02T09:51:41Z",
  "source": "Microsoft-Windows-Diagnostics-Performance/Operational",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "warning",
  "message": "<The description for Event ID ( 101 ) in Source ( 'Microsoft-Windows-Diagnostics-Performance' ) could not be found. It contains the following insertion string(s):'2025-06-02T13:49:37.8896136Z, 11, Sysmon.exe, 24, System activity monitor, 6, 14.13, 766, 388, 22, C:\\\\Windows\\\\Sysmon.exe, 20, Sysinternals Sysmon, 36, Sysinternals - www.sysinternals.com'.>",
  "raw_data": null,
  "additional_fields": {
    "record_number": 206,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "2025-06-02T13:49:37.8896136Z",
      "11",
      "Sysmon.exe",
      "24",
      "System activity monitor",
      "6",
      "14.13",
      "766",
      "388",
      "22",
      "C:\\Windows\\Sysmon.exe",
      "20",
      "Sysinternals Sysmon",
      "36",
      "Sysinternals - www.sysinternals.com"
    ],
    "data": null,
    "microsoft_source": "Microsoft-Windows-Diagnostics-Performance/Operational",
    "event_id": 101,
    "event_category": 4002,
    "event_type": 2,
    "event_description": "Application stopped",
    "metadata": {
      "collection_time": "2025-06-02T10:54:30.959989",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "application_lifecycle"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 2: {
  "log_id": "f7645ff1-9007-4e38-8fa8-c1393020f88b",
  "timestamp": "2025-06-02T09:51:41Z",
  "source": "Microsoft-Windows-Diagnostics-Performance/Operational",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "warning",
  "message": "<The description for Event ID ( 101 ) in Source ( 'Microsoft-Windows-Diagnostics-Performance' ) could not be found. It contains the following insertion string(s):'2025-06-02T13:49:37.8896136Z, 13, DiagsCap.exe, 1, , 12, 1.78.4195.0, 1409, 409, 107, C:\\\\Windows\\\\System32\\\\DriverStore\\\\FileRepository\\\\hpcustomcapcomp.inf_amd64_f2bc3e822f15dc0b\\\\x64\\\\DiagsCap.exe, 9, DiagsCap, 8, HP Inc.'.>",
  "raw_data": null,
  "additional_fields": {
    "record_number": 205,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "2025-06-02T13:49:37.8896136Z",
      "13",
      "DiagsCap.exe",
      "1",
      "",
      "12",
      "1.78.4195.0",
      "1409",
      "409",
      "107",
      "C:\\Windows\\System32\\DriverStore\\FileRepository\\hpcustomcapcomp.inf_amd64_f2bc3e822f15dc0b\\x64\\DiagsCap.exe",
      "9",
      "DiagsCap",
      "8",
      "HP Inc."
    ],
    "data": null,
    "microsoft_source": "Microsoft-Windows-Diagnostics-Performance/Operational",
    "event_id": 101,
    "event_category": 4002,
    "event_type": 2,
    "event_description": "Application stopped",
    "metadata": {
      "collection_time": "2025-06-02T10:54:30.959989",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "application_lifecycle"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 3: {
  "log_id": "477fba16-c908-46b3-9bd8-6cbf2c7fe0bf",
  "timestamp": "2025-06-02T09:51:41Z",
  "source": "Microsoft-Windows-Diagnostics-Performance/Operational",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "warning",
  "message": "<The description for Event ID ( 101 ) in Source ( 'Microsoft-Windows-Diagnostics-Performance' ) could not be found. It contains the following insertion string(s):'2025-06-02T13:49:37.8896136Z, 26, ExpressVPN.VpnService.exe, 22, ExpressVPN.VpnService, 11, **********, 827, 503, 69, C:\\\\Program Files (x86)\\\\ExpressVPN\\\\services\\\\ExpressVPN.VpnService.exe, 11, ExpressVPN, 11, ExpressVPN'.>",
  "raw_data": null,
  "additional_fields": {
    "record_number": 204,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "2025-06-02T13:49:37.8896136Z",
      "26",
      "ExpressVPN.VpnService.exe",
      "22",
      "ExpressVPN.VpnService",
      "11",
      "**********",
      "827",
      "503",
      "69",
      "C:\\Program Files (x86)\\ExpressVPN\\services\\ExpressVPN.VpnService.exe",
      "11",
      "ExpressVPN",
      "11",
      "ExpressVPN"
    ],
    "data": null,
    "microsoft_source": "Microsoft-Windows-Diagnostics-Performance/Operational",
    "event_id": 101,
    "event_category": 4002,
    "event_type": 2,
    "event_description": "Application stopped",
    "metadata": {
      "collection_time": "2025-06-02T10:54:30.959989",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "application_lifecycle"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:54:31 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - API request failed with status 400: {"status":"fail","error":{"statusCode":400,"isOperational":true,"status":"fail"},"message":"Validation failed","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Problematic logs that caused validation error:
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 1: {
  "log_id": "0d6ae921-0134-4d3e-a465-0e25fa41fb95",
  "timestamp": "2025-06-02T09:52:20Z",
  "source": "Application",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "Killing process tree of process 23756 for service QsirchInitialized with exit code 0",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39424,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "QsirchInitialized",
      "23756",
      "0"
    ],
    "data": null,
    "event_id": 1023,
    "event_category": 0,
    "event_type": 4,
    "event_description": "Application event",
    "metadata": {
      "collection_time": "2025-06-02T10:54:30.960991",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "general_application"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 2: {
  "log_id": "0ddf648a-2169-4062-8c21-4ee01cb0617e",
  "timestamp": "2025-06-02T09:52:20Z",
  "source": "Application",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "Program C:\\Program Files (x86)\\QNAP\\Qsirch\\utils\\qsirch.bat for service QsirchInitialized exited with return code 0.",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39423,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": [
      "C:\\Program Files (x86)\\QNAP\\Qsirch\\utils\\qsirch.bat",
      "QsirchInitialized",
      "0"
    ],
    "data": null,
    "event_id": 1013,
    "event_category": 0,
    "event_type": 4,
    "event_description": "Application event",
    "metadata": {
      "collection_time": "2025-06-02T10:54:30.960991",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "general_application"
    }
  }
}
2025-06-02 10:54:31 - utils.simple_api_client - ERROR - Log 3: {
  "log_id": "378c423e-b221-44b0-abd1-f113d98e4a36",
  "timestamp": "2025-06-02T09:52:20Z",
  "source": "Application",
  "source_type": "application",
  "host": "DESKTOP-PLUAU4C",
  "log_level": "info",
  "message": "The Windows Security Center Service has started.",
  "raw_data": null,
  "additional_fields": {
    "record_number": 39422,
    "computer_name": "DESKTOP-PLUAU4C",
    "string_inserts": null,
    "data": null,
    "event_id": 1,
    "event_category": 0,
    "event_type": 4,
    "event_description": "Application event",
    "metadata": {
      "collection_time": "2025-06-02T10:54:30.960991",
      "agent_version": "1.0.0",
      "standardizer_version": "1.0.0",
      "application_log": true,
      "application_category": "general_application"
    }
  }
}
2025-06-02 10:54:32 - root - INFO - Agent is not running
2025-06-02 10:58:48 - root - INFO - Event Log Collector initialized
2025-06-02 10:58:48 - root - INFO - Application Log Collector initialized
2025-06-02 10:58:48 - root - INFO - System Log Collector initialized
2025-06-02 10:58:48 - root - INFO - Network Log Collector initialized
2025-06-02 10:58:48 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:58:48 - root - INFO - Packet Collector initialized
2025-06-02 10:58:48 - utils.simple_api_client - INFO - Simple API client configured: http://localhost:5000/api/v1/logs
2025-06-02 10:58:48 - utils.simple_api_client - INFO - Simple API client started
2025-06-02 10:58:48 - utils.simple_api_client - INFO - API connection test successful
2025-06-02 10:58:48 - root - INFO - API connection test successful (response time: 0.02s)
2025-06-02 10:58:48 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 10:58:48 - root - INFO - Logging Agent initialized successfully
2025-06-02 10:58:48 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 10:58:48 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 10:58:48 - root - INFO - Logging Agent started successfully
2025-06-02 10:58:48 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 10:58:53 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:53 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:53 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:53 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:53 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:53 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:53 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:53 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:53 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:53 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:58 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:58 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:58 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:58 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:58 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:58 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:58 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:58 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:58 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:58 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:58 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:58 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:58:58 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:58:58 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:59:02 - root - INFO - Stopping Logging Agent...
2025-06-02 10:59:03 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:59:03 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:59:03 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:59:03 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:59:03 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:59:03 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:59:03 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:59:03 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:59:03 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:59:03 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:59:03 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:59:03 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:59:03 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 10:59:03 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 10:59:07 - root - INFO - Agent is not running
2025-06-02 11:00:31 - root - INFO - Event Log Collector initialized
2025-06-02 11:00:31 - root - INFO - Application Log Collector initialized
2025-06-02 11:00:31 - root - INFO - System Log Collector initialized
2025-06-02 11:00:31 - root - INFO - Network Log Collector initialized
2025-06-02 11:00:31 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 11:00:31 - root - INFO - Packet Collector initialized
2025-06-02 11:00:31 - utils.simple_api_client - INFO - Simple API client configured: http://localhost:5000/api/v1/logs
2025-06-02 11:00:31 - utils.simple_api_client - INFO - Simple API client started
2025-06-02 11:00:31 - utils.simple_api_client - INFO - API connection test successful
2025-06-02 11:00:31 - root - INFO - API connection test successful (response time: 0.02s)
2025-06-02 11:00:31 - root - INFO - Async API client initialized for dashboard integration
2025-06-02 11:00:31 - root - INFO - Logging Agent initialized successfully
2025-06-02 11:00:31 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902} with filter: ''
2025-06-02 11:00:31 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{A5D95191-6C2B-4EF9-AD37-1FA7692E7902}
2025-06-02 11:00:31 - root - INFO - Logging Agent started successfully
2025-06-02 11:00:31 - audit_logger - INFO - Python Logging Agent service started
2025-06-02 11:00:36 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:36 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:36 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:36 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:36 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:36 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:36 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:36 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:36 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:36 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:41 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:41 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:41 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:41 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:41 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:41 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:41 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:41 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:41 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:41 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:41 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:41 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:41 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:41 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:46 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:46 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:46 - utils.simple_api_client - WARNING - API request failed with status 429: Too many requests from this IP, please try again later.
2025-06-02 11:00:46 - utils.simple_api_client - INFO - Retrying in 5 seconds (attempt 1/3)
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 1 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=1, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:51 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:51 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:52 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:52 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:56 - utils.simple_api_client - WARNING - Complex source name 'PacketCapture' simplified to 'Application'
2025-06-02 11:00:56 - utils.simple_api_client - WARNING - Complex source name 'PacketCapture' simplified to 'Application'
2025-06-02 11:00:56 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:56 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:56 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:56 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:56 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:56 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:56 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:56 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:56 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:56 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:56 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:00:56 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:00:59 - root - INFO - Stopping Logging Agent...
2025-06-02 11:01:01 - utils.simple_api_client - WARNING - Complex source name 'PacketCapture' simplified to 'Application'
2025-06-02 11:01:01 - utils.simple_api_client - WARNING - Complex source name 'PacketCapture' simplified to 'Application'
2025-06-02 11:01:01 - utils.simple_api_client - WARNING - Complex source name 'PacketCapture' simplified to 'Application'
2025-06-02 11:01:01 - utils.simple_api_client - WARNING - Complex source name 'PacketCapture' simplified to 'Application'
2025-06-02 11:01:01 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:01:01 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:01:01 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:01:01 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:01:01 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:01:01 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:01:01 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:01:01 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:01:01 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:01:01 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:01:01 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:01:01 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:01:01 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-02 11:01:01 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-02 11:01:04 - root - INFO - Agent is not running
2025-06-10 14:37:18 - root - INFO - Event Log Collector initialized
2025-06-10 14:37:18 - root - INFO - Application Log Collector initialized
2025-06-10 14:37:18 - root - INFO - System Log Collector initialized
2025-06-10 14:37:18 - root - INFO - Network Log Collector initialized
2025-06-10 14:37:18 - logging_agent.collectors.packet_collector - INFO - Auto-selected interface: \Device\NPF_{1017022A-C132-4603-B463-2B94AE0B9765}
2025-06-10 14:37:18 - root - INFO - Packet Collector initialized
2025-06-10 14:37:18 - utils.simple_api_client - INFO - Simple API client configured: http://localhost:5000/api/v1/logs
2025-06-10 14:37:18 - utils.simple_api_client - INFO - Simple API client started
2025-06-10 14:37:19 - utils.simple_api_client - INFO - API connection test successful
2025-06-10 14:37:19 - root - INFO - API connection test successful (response time: 0.02s)
2025-06-10 14:37:19 - root - INFO - Async API client initialized for dashboard integration
2025-06-10 14:37:19 - root - INFO - Logging Agent initialized successfully
2025-06-10 14:37:19 - logging_agent.collectors.packet_collector - INFO - Starting packet capture on \Device\NPF_{1017022A-C132-4603-B463-2B94AE0B9765} with filter: ''
2025-06-10 14:37:19 - logging_agent.collectors.packet_collector - INFO - Started packet capture on interface \Device\NPF_{1017022A-C132-4603-B463-2B94AE0B9765}
2025-06-10 14:37:19 - root - INFO - Logging Agent started successfully
2025-06-10 14:37:19 - audit_logger - INFO - Python Logging Agent service started
2025-06-10 14:37:24 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:24 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:24 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:24 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:34 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:34 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:34 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:34 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:34 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:34 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:39 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:39 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:39 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:39 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:39 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:39 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:44 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:44 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:44 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:44 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:44 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:44 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:44 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:44 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:49 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:49 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:49 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:49 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:49 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:49 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:54 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:54 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:54 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:54 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:54 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:54 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:59 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:59 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:59 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:59 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:59 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:59 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:37:59 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:37:59 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:04 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:04 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:04 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:04 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:04 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:04 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:09 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:09 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:09 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:09 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:09 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:09 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:14 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:14 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:14 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:14 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:14 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:14 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:19 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:19 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:19 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:19 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:19 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:19 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:24 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:24 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:24 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:24 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:24 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:24 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:34 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:34 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:34 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:34 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:34 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:34 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:39 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:39 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:39 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:39 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:39 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:39 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:44 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:44 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:44 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:44 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:44 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:44 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:49 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:49 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:49 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:49 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:49 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:49 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:54 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:54 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:54 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:54 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:54 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:54 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:54 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:54 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:59 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:59 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:59 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:59 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:59 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:59 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:38:59 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:38:59 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:04 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:04 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:04 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:04 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:04 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:04 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:09 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:09 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:09 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:09 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:09 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:09 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:14 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:14 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:14 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:14 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:14 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:14 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:19 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:19 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:19 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:19 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:19 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:19 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:19 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:19 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:24 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:24 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:24 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:24 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:24 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:24 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:28 - root - INFO - Stopping Logging Agent...
2025-06-10 14:39:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:29 - utils.simple_api_client - INFO - Successfully sent batch of 10 logs
2025-06-10 14:39:29 - utils.simple_api_client - INFO - API response: processed=10, failed=0
2025-06-10 14:39:33 - logging_agent.collectors.packet_collector - INFO - Stopped packet capture
2025-06-10 14:39:34 - utils.simple_api_client - INFO - Simple API client stopped
2025-06-10 14:39:34 - root - INFO - Logging Agent stopped successfully
2025-06-10 14:39:34 - audit_logger - INFO - Python Logging Agent service stopped
