const axios = require('axios');

async function testFrontendLogRetrieval() {
  try {
    console.log('🔍 Testing frontend log retrieval...');

    // First, let's get a JWT token for authentication
    const loginResponse = await axios.post('http://localhost:5000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'Admin123!'
    });

    const token = loginResponse.data.data.token;
    console.log('✅ Got authentication token');

    // Now fetch logs using the same endpoint the frontend uses
    const logsResponse = await axios.get('http://localhost:5000/api/v1/logs', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      params: {
        page: 1,
        limit: 50
      }
    });

    console.log('✅ Logs retrieved successfully!');
    console.log('Status:', logsResponse.status);
    console.log('Total logs found:', logsResponse.data.data.pagination.totalCount);
    console.log('Logs on this page:', logsResponse.data.data.logs.length);
    
    if (logsResponse.data.data.logs.length > 0) {
      console.log('\n📋 Recent logs:');
      logsResponse.data.data.logs.slice(0, 3).forEach((log, index) => {
        console.log(`${index + 1}. ${log.logId} - ${log.message} (${log.timestamp})`);
      });
    } else {
      console.log('❌ No logs found in the response!');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testFrontendLogRetrieval();
